<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('items', function (Blueprint $table) {
            if (!Schema::hasColumn('items', 'guid')) {
                $table->string('guid')->nullable()->unique()->after('id');
            }
            if (!Schema::hasColumn('items', 'code')) {
                $table->string('code')->nullable()->after('guid');
            }
            if (!Schema::hasColumn('items', 'barcode')) {
                $table->string('barcode')->nullable()->after('code');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('items', function (Blueprint $table) {
            if (Schema::hasColumn('items', 'guid')) {
                $table->dropColumn('guid');
            }
            if (Schema::hasColumn('items', 'code')) {
                $table->dropColumn('code');
            }
            if (Schema::hasColumn('items', 'barcode')) {
                $table->dropColumn('barcode');
            }
        });
    }
};
