<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOtpHitCountColsInPhoneVerificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('phone_verifications', function (Blueprint $table) {
            $table->tinyInteger('otp_hit_count')->default('0');
            $table->boolean('is_blocked')->default('0');
            $table->boolean('is_temp_blocked')->default('0');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('phone_verifications', function (Blueprint $table) {
            $table->dropColumn('otp_hit_count');
            $table->dropColumn('is_blocked');
            $table->dropColumn('is_temp_blocked');
        });
    }
}