<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة إعدادات HTD SMS إلى جدول addon_settings
        DB::table('addon_settings')->insert([
            'id' => Str::uuid(),
            'key_name' => 'htd_sms',
            'live_values' => json_encode([
                'status' => 0,
                'api_id' => '69df7c4302020b5a21ae03c650f1f7c4',
                'sender_name' => 'SenderName',
                'otp_template' => 'Your OTP is: #OTP#'
            ]),
            'test_values' => json_encode([
                'status' => 0,
                'api_id' => '69df7c4302020b5a21ae03c650f1f7c4',
                'sender_name' => 'SenderName',
                'otp_template' => 'Your OTP is: #OTP#'
            ]),
            'settings_type' => 'sms_config',
            'mode' => 'live',
            'is_active' => 1,
            'created_at' => now(),
            'updated_at' => now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // حذف إعدادات HTD SMS
        DB::table('addon_settings')->where('key_name', 'htd_sms')->delete();
    }
};
