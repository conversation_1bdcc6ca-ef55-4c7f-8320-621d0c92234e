@foreach($orders as $k=>$order)
    <tr>
        <td>
            @if($order->close_financial_dm == false && $order->order_status == 'delivered' && $order->payment_method == 'digital_payment')
                <input type="checkbox" name="pending_order_ids[]" value="{{ $order->id }}" class="form-check-input pending-order-checkbox">
            @endif
        </td>
        <td>{{$k+1}}</td>
        <td>
            <a href="{{route('vendor.order.details',$order->id)}}" class="text-primary">
                #{{$order->id}}
            </a>
        </td>
        <td>
            @if($order->customer)
                <div class="d-flex align-items-center">
                    <div>
                        <h6 class="mb-0">{{$order->customer->f_name}} {{$order->customer->l_name}}</h6>
                        <small class="text-muted">{{$order->customer->phone}}</small>
                    </div>
                </div>
            @else
                <span class="text-muted">{{translate('messages.customer_not_found')}}</span>
            @endif
        </td>
        <td>
            <div class="text-right">
                <div>{{ \App\CentralLogics\Helpers::currency_symbol() }}{{ $order->order_amount }}</div>
                @if($order->delivery_charge > 0)
                    <small class="text-muted">{{translate('messages.delivery')}}: {{ \App\CentralLogics\Helpers::currency_symbol() }}{{ $order->delivery_charge }}</small>
                @endif
            </div>
        </td>
        <td>
            <span class="badge badge-soft-info">
                {{ translate('messages.' . $order->payment_method) }}
            </span>
        </td>
        <td>
            @if($order->order_status == 'pending')
                <span class="badge badge-soft-warning">{{translate('messages.pending')}}</span>
            @elseif($order->order_status == 'confirmed')
                <span class="badge badge-soft-info">{{translate('messages.confirmed')}}</span>
            @elseif($order->order_status == 'processing')
                <span class="badge badge-soft-primary">{{translate('messages.processing')}}</span>
            @elseif($order->order_status == 'picked_up')
                <span class="badge badge-soft-secondary">{{translate('messages.picked_up')}}</span>
            @elseif($order->order_status == 'delivered')
                <span class="badge badge-soft-success">{{translate('messages.delivered')}}</span>
            @elseif($order->order_status == 'canceled')
                <span class="badge badge-soft-danger">{{translate('messages.canceled')}}</span>
            @else
                <span class="badge badge-soft-dark">{{translate('messages.' . $order->order_status)}}</span>
            @endif
        </td>
        <td>
            @if($order->close_financial_dm == false && $order->order_status == 'delivered' && $order->payment_method == 'digital_payment')
                <span class="badge badge-soft-warning">
                    <i class="tio-time"></i> {{translate('messages.pending')}}
                </span>
            @else
                <span class="badge badge-soft-success">
                    <i class="tio-checkmark-circle"></i> {{translate('messages.closed')}}
                </span>
            @endif
        </td>
        <td>
            <div class="text-muted">
                {{ \Carbon\Carbon::parse($order->created_at)->format('Y-m-d') }}
                <br>
                <small>{{ \Carbon\Carbon::parse($order->created_at)->format('H:i') }}</small>
            </div>
        </td>
        <td>
            <div class="btn-group" role="group">
                <a href="{{route('vendor.order.details',$order->id)}}" class="btn btn-sm btn-outline-primary" title="{{translate('messages.view_details')}}">
                    <i class="tio-visible"></i>
                </a>
                @if($order->close_financial_dm == false && $order->order_status == 'delivered' && $order->payment_method == 'digital_payment')
                    <button type="button" class="btn btn-sm btn-warning close-single-order-btn" data-order-id="{{ $order->id }}" title="{{translate('messages.close_earnings')}}">
                        <i class="tio-checkmark-circle"></i>
                    </button>
                @endif
            </div>
        </td>
    </tr>
@endforeach

<form id="close-financial-form" method="POST" action="{{route('vendor.delivery-man.close-financial')}}" style="display: none;">
    @csrf
    <input type="hidden" name="delivery_man_id" value="{{ request()->route('id') }}">
    <input type="hidden" name="order_ids[]" id="order-id-input">
</form>

<script>
$(document).ready(function() {
    $(document).on('click', '.close-single-order-btn', function() {
        const orderId = $(this).data('order-id');
        
        Swal.fire({
            title: '{{translate('messages.are_you_sure')}}',
            text: `{{translate('messages.close_financial_for_order')}} #${orderId}?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '{{translate('messages.yes_close_it')}}',
            cancelButtonText: '{{translate('messages.cancel')}}'
        }).then((result) => {
            if (result.isConfirmed) {
                $('#order-id-input').val(orderId);
                $('#close-financial-form').submit();
            }
        });
    });
});
</script>