@extends('layouts.vendor.app')

@section('title',translate('messages.Delivery Man Preview'))


@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">
                        <span class="page-header-icon">
                            <img src="{{asset('public/assets/admin/img/deliveryman.png')}}" class="w--30" alt="">
                        </span>
                        <span>
                            {{$dm['f_name'].' '.$dm['l_name']}}
                        </span>
                    </h1>
                </div>
                <div class="col-sm-auto">
                    <!-- Export Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" id="exportDropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="tio-download-to"></i> {{translate('messages.export')}}
                        </button>
                        <div class="dropdown-menu" aria-labelledby="exportDropdown">
                            <a class="dropdown-item" href="{{route('vendor.delivery-man.earnings-export', [$dm->id, 'excel'])}}">
                                <i class="tio-file-excel"></i> {{translate('messages.earnings')}} (Excel)
                            </a>
                            <a class="dropdown-item" href="{{route('vendor.delivery-man.earnings-export', [$dm->id, 'csv'])}}">
                                <i class="tio-file-text"></i> {{translate('messages.earnings')}} (CSV)
                            </a>
                            <a class="dropdown-item" href="{{route('vendor.delivery-man.disbursement-export', [$dm->id, 'excel'])}}">
                                <i class="tio-file-excel"></i> {{translate('messages.disbursement')}} (Excel)
                            </a>
                            <a class="dropdown-item" href="{{route('vendor.delivery-man.disbursement-export', [$dm->id, 'csv'])}}">
                                <i class="tio-file-text"></i> {{translate('messages.disbursement')}} (CSV)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="js-nav-scroller hs-nav-scroller-horizontal">
                <ul class="nav nav-tabs mb-3 border-0 nav--tabs">
                    <li class="nav-item">
                        <a class="nav-link" href="{{route('vendor.delivery-man.preview', ['id'=>$dm->id, 'tab'=> 'info'])}}"  aria-disabled="true">{{translate('messages.info')}}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{route('vendor.delivery-man.preview', ['id'=>$dm->id, 'tab'=> 'transaction'])}}"  aria-disabled="true">{{translate('messages.transaction')}}</a>
                    </li>
                </ul>
            </div>
        </div>
        <!-- End Page Header -->

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="card-title text-primary">{{ $orders->total() }}</h4>
                        <p class="card-text">
                            {{translate('messages.total_orders')}}
                            @if($financial_status == 'pending')
                                ({{translate('messages.pending_earnings')}})
                            @elseif($financial_status == 'closed')
                                ({{translate('messages.closed_earnings')}})
                            @else
                                ({{translate('messages.all')}})
                            @endif
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="card-title text-success">{{ $closed_earning_orders_count }}</h4>
                        <p class="card-text">{{translate('messages.closed_earnings')}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="card-title text-warning">{{ $pending_earning_orders_count }}</h4>
                        <p class="card-text">{{translate('messages.pending_earnings')}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h4 class="card-title text-info">{{ $digital_transaction->total() }}</h4>
                        <p class="card-text">{{translate('messages.total_transactions')}}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Orders Card -->
        <div class="card mb-3 mb-lg-5 mt-2">
            <div class="card-header py-2 border-0">
                <div class="search--button-wrapper">
                    <h4 class="card-title">{{ translate('messages.all_orders')}} ({{translate('messages.digital_payment_only')}})</h4>
                    <div class="d-flex gap-3">
                        <!-- Financial Status Filter -->
                        <form method="GET" class="d-flex align-items-center">
                            <input type="hidden" name="search" value="{{ $search }}">
                            <select name="financial_status" class="form-control" onchange="this.form.submit()" style="width: 200px;">
                                <option value="pending" {{ $financial_status == 'pending' ? 'selected' : '' }}>
                                    {{translate('messages.pending_earnings')}}
                                </option>
                                <option value="closed" {{ $financial_status == 'closed' ? 'selected' : '' }}>
                                    {{translate('messages.closed_earnings')}}
                                </option>
                                <option value="all" {{ $financial_status == 'all' ? 'selected' : '' }}>
                                    {{translate('messages.all')}}
                                </option>
                            </select>
                        </form>

                        <!-- Search Form -->
                        <form method="GET" class="search-form">
                            <input type="hidden" name="financial_status" value="{{ $financial_status }}">
                            <div class="input-group input--group">
                                <input type="search" name="search" value="{{ $search }}" class="form-control" placeholder="{{translate('messages.search_by_order_id_customer')}}" aria-label="Search">
                                <button type="submit" class="btn btn--secondary"><i class="tio-search"></i></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Body -->
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                        <thead class="thead-light">
                            <tr>
                                <th class="border-0">
                                    <input type="checkbox" id="select-all-orders" class="form-check-input">
                                </th>
                                <th class="border-0">{{translate('messages.sl#')}}</th>
                                <th class="border-0">{{translate('messages.order_id')}}</th>
                                <th class="border-0">{{translate('messages.customer')}}</th>
                                <th class="border-0">{{translate('messages.order_amount')}}</th>
                                <th class="border-0">{{translate('messages.payment_method')}}</th>
                                <th class="border-0">{{translate('messages.order_status')}}</th>
                                <th class="border-0">{{translate('messages.financial_status')}}</th>
                                <th class="border-0">{{translate('messages.date')}}</th>
                                <th class="border-0">{{translate('messages.action')}}</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table-body">
                        @foreach($orders as $k=>$order)
                            <tr>
                                <td>
                                    @if($order->close_financial_dm == false && $order->order_status == 'delivered' && $order->payment_method == 'digital_payment')
                                        <input type="checkbox" name="pending_order_ids[]" value="{{ $order->id }}" class="form-check-input pending-order-checkbox">
                                    @endif
                                </td>
                                <td>{{$k+$orders->firstItem()}}</td>
                                <td>
                                    <a href="{{route('vendor.order.details',$order->id)}}" class="text-primary">
                                        #{{$order->id}}
                                    </a>
                                </td>
                                <td>
                                    @if($order->customer)
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{$order->customer->f_name}} {{$order->customer->l_name}}</h6>
                                                <small class="text-muted">{{$order->customer->phone}}</small>
                                            </div>
                                        </div>
                                    @else
                                        <span class="text-muted">{{translate('messages.customer_not_found')}}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="text-right">
                                        <div>{{ \App\CentralLogics\Helpers::currency_symbol() }}{{ $order->order_amount }}</div>
                                        @if($order->delivery_charge > 0)
                                            <small class="text-muted">{{translate('messages.delivery')}}: {{ \App\CentralLogics\Helpers::currency_symbol() }}{{ $order->delivery_charge }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-soft-info">
                                        {{ translate('messages.' . $order->payment_method) }}
                                    </span>
                                </td>
                                <td>
                                    @if($order->order_status == 'pending')
                                        <span class="badge badge-soft-warning">{{translate('messages.pending')}}</span>
                                    @elseif($order->order_status == 'confirmed')
                                        <span class="badge badge-soft-info">{{translate('messages.confirmed')}}</span>
                                    @elseif($order->order_status == 'processing')
                                        <span class="badge badge-soft-primary">{{translate('messages.processing')}}</span>
                                    @elseif($order->order_status == 'picked_up')
                                        <span class="badge badge-soft-secondary">{{translate('messages.picked_up')}}</span>
                                    @elseif($order->order_status == 'delivered')
                                        <span class="badge badge-soft-success">{{translate('messages.delivered')}}</span>
                                    @elseif($order->order_status == 'canceled')
                                        <span class="badge badge-soft-danger">{{translate('messages.canceled')}}</span>
                                    @else
                                        <span class="badge badge-soft-dark">{{translate('messages.' . $order->order_status)}}</span>
                                    @endif
                                </td>
                                <td>
                                    @if($order->close_financial_dm == false && $order->order_status == 'delivered' && $order->payment_method == 'digital_payment')
                                        <span class="badge badge-soft-warning">
                                            <i class="tio-time"></i> {{translate('messages.pending')}}
                                        </span>
                                    @else
                                        <span class="badge badge-soft-success">
                                            <i class="tio-checkmark-circle"></i> {{translate('messages.closed')}}
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="text-muted">
                                        {{ \Carbon\Carbon::parse($order->created_at)->format('Y-m-d') }}
                                        <br>
                                        <small>{{ \Carbon\Carbon::parse($order->created_at)->format('H:i') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{route('vendor.order.details',$order->id)}}" class="btn btn-sm btn-outline-primary" title="{{translate('messages.view_details')}}">
                                            <i class="tio-visible"></i>
                                        </a>
                                        @if($order->close_financial_dm == false && $order->order_status == 'delivered' && $order->payment_method == 'digital_payment')
                                            <button type="button" class="btn btn-sm btn-warning" onclick="closeSingleOrder({{ $order->id }})" title="{{translate('messages.close_earnings')}}">
                                                <i class="tio-checkmark-circle"></i>
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- End Body -->
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <button type="button" id="close-selected-btn" class="btn btn-warning" style="display: none;" onclick="closeSelectedOrders()">
                            <i class="tio-checkmark-circle"></i> {{translate('messages.close_selected_earnings')}}
                        </button>
                    </div>
                    <div>
                        {!!$orders->links()!!}
                    </div>
                </div>
            </div>
        </div>
        <!-- End Card -->

        <!-- Order Transactions Card -->
        <div class="card mb-3 mb-lg-5 mt-2">
            <div class="card-header py-2 border-0">
                <div class="search--button-wrapper">
                    <h4 class="card-title">{{ translate('messages.order_transactions')}}</h4>
                    <form action="javascript:" id="search-form" class="search-form">
                        @csrf
                        <input type="hidden" name="dm_id" value="{{ $dm->id }}">
                        <!-- Search -->
                        <div class="input-group input--group">
                            <input value="{{request()?->search ?? ''}}"  required type="search" name="search" class="form-control" placeholder="{{translate('messages.ex_search_order_id ')}}" aria-label="Search">
                            <button type="submit" class="btn btn--secondary"><i class="tio-search"></i></button>
                        </div>
                        <!-- End Search -->
                    </form>

                </div>
            </div>
            <!-- Body -->
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table id="datatable"
                        class="table table-borderless table-thead-bordered table-nowrap justify-content-between table-align-middle card-table">
                        <thead class="thead-light">
                            <tr>
                                <th class="border-0">{{translate('messages.sl#')}}</th>
                                <th class="border-0">{{translate('messages.order_id')}}</th>
                                <th class="border-0">{{translate('messages.deliveryman_earned')}}</th>
                                <th class="border-0">{{translate('messages.date')}}</th>
                            </tr>
                        </thead>
                        <tbody id="set-rows">
                        @foreach($digital_transaction as $k=>$dt)
                            <tr>
                                <td>{{$k+$digital_transaction->firstItem()}}</td>
                                <td><a href="{{route('vendor.order.details',$dt->order_id)}}">{{$dt->order_id}}</a></td>
                                <td>{{$dt->original_delivery_charge}}</td>
                                <td>{{$dt->created_at->format('Y-m-d')}}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- End Body -->
            <div class="card-footer">
                {!!$digital_transaction->links()!!}
            </div>
        </div>
        <!-- End Card -->
    </div>
@endsection

@push('script_2')
<script>
    console.log('Transaction page JavaScript loaded');
    // Transaction search
    $('#search-form').on('submit', function (e) {
        e.preventDefault();
        let formData = new FormData(this);
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        $.post({
            url: '{{route('vendor.delivery-man.transaction-search')}}',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            beforeSend: function () {
                $('#loading').show();
            },
            success: function (data) {
                $('#set-rows').html(data.view);
                $('.page-area').hide();
            },
            complete: function () {
                $('#loading').hide();
            },
        });
    });

    // Note: Search is now handled by GET forms, no AJAX needed

    // Select all orders checkbox
    $('#select-all-orders').on('change', function() {
        $('.pending-order-checkbox').prop('checked', this.checked);
        toggleCloseSelectedButton();
    });

    // Individual checkbox change
    $(document).on('change', '.pending-order-checkbox', function() {
        toggleCloseSelectedButton();

        // Update select all checkbox
        const totalCheckboxes = $('.pending-order-checkbox').length;
        const checkedCheckboxes = $('.pending-order-checkbox:checked').length;
        $('#select-all-orders').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Toggle close selected button visibility
    function toggleCloseSelectedButton() {
        const checkedCount = $('.pending-order-checkbox:checked').length;
        if (checkedCount > 0) {
            $('#close-selected-btn').show();
            $('#close-selected-btn').text(`{{translate('messages.close_selected_earnings')}} (${checkedCount})`);
        } else {
            $('#close-selected-btn').hide();
        }
    }

    // Close selected orders
    function closeSelectedOrders() {
        console.log('closeSelectedOrders function called');
        const selectedOrders = [];
        $('.pending-order-checkbox:checked').each(function() {
            selectedOrders.push($(this).val());
        });
        console.log('Selected orders:', selectedOrders);

        if (selectedOrders.length === 0) {
            Swal.fire({
                type: 'warning',
                title: '{{translate('messages.no_orders_selected')}}',
                text: '{{translate('messages.please_select_orders_to_close')}}'
            });
            return;
        }

        Swal.fire({
            title: '{{translate('messages.are_you_sure')}}',
            text: `{{translate('messages.close_financial_for')}} ${selectedOrders.length} {{translate('messages.orders')}}?`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '{{translate('messages.yes_close_it')}}',
            cancelButtonText: '{{translate('messages.cancel')}}'
        }).then((result) => {
            console.log('Multiple orders SweetAlert result:', result);
            if (result.value === true || result.isConfirmed) {
                console.log('User confirmed! Closing multiple orders:', selectedOrders);

                // Show loading immediately
                Swal.fire({
                    title: '{{translate('messages.processing')}}',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading()
                    }
                });

                console.log('About to make AJAX request for multiple orders...');
                console.log('URL:', '{{route('vendor.delivery-man.close-financial')}}');
                console.log('Data:', {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    delivery_man_id: {{ $dm->id }},
                    order_ids: selectedOrders
                });

                // Make AJAX request
                $.ajax({
                    url: '{{route('vendor.delivery-man.close-financial')}}',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        delivery_man_id: {{ $dm->id }},
                        order_ids: selectedOrders
                    },
                    beforeSend: function() {
                        console.log('Multiple orders AJAX request started...');
                    },
                    success: function (data) {
                        console.log('Multiple orders AJAX Success:', data);
                        Swal.fire({
                            type: 'success',
                            title: '{{translate('messages.success')}}',
                            text: data.message || 'تم الإغلاق بنجاح'
                        }).then(() => {
                            console.log('Reloading page...');
                            location.reload();
                        });
                    },
                    error: function (xhr, status, error) {
                        console.log('Multiple orders AJAX Error:', xhr, status, error);
                        console.log('Response Text:', xhr.responseText);
                        let message = '{{translate('messages.something_went_wrong')}}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            type: 'error',
                            title: '{{translate('messages.error')}}',
                            text: message
                        });
                    }
                });
            } else {
                console.log('User cancelled the multiple orders action');
            }
        });
    }

    // Close single order
    function closeSingleOrder(orderId) {
        Swal.fire({
            title: '{{translate('messages.are_you_sure')}}',
            text: `{{translate('messages.close_financial_for_order')}} #${orderId}?`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '{{translate('messages.yes_close_it')}}',
            cancelButtonText: '{{translate('messages.cancel')}}'
        }).then((result) => {
            console.log('SweetAlert result:', result);
            if (result.value === true || result.isConfirmed) {
                console.log('User confirmed! Closing single order:', orderId);

                // Show loading immediately
                Swal.fire({
                    title: '{{translate('messages.processing')}}',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading()
                    }
                });

                console.log('About to make AJAX request...');
                console.log('URL:', '{{route('vendor.delivery-man.close-financial')}}');
                console.log('Data:', {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    delivery_man_id: {{ $dm->id }},
                    order_ids: [orderId]
                });

                // Make AJAX request
                $.ajax({
                    url: '{{route('vendor.delivery-man.close-financial')}}',
                    type: 'POST',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        delivery_man_id: {{ $dm->id }},
                        order_ids: [orderId]
                    },
                    beforeSend: function() {
                        console.log('AJAX request started...');
                    },
                    success: function (data) {
                        console.log('AJAX Success:', data);
                        Swal.fire({
                            type: 'success',
                            title: '{{translate('messages.success')}}',
                            text: data.message || 'تم الإغلاق بنجاح'
                        }).then(() => {
                            console.log('Reloading page...');
                            location.reload();
                        });
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr, status, error);
                        console.log('Response Text:', xhr.responseText);
                        let message = '{{translate('messages.something_went_wrong')}}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            type: 'error',
                            title: '{{translate('messages.error')}}',
                            text: message
                        });
                    }
                });
            } else {
                console.log('User cancelled the action');
            }
        });
    }

    // Close all pending earnings
    function closePendingEarnings() {
        const allPendingOrders = [];
        $('.pending-order-checkbox').each(function() {
            allPendingOrders.push($(this).val());
        });

        if (allPendingOrders.length === 0) {
            Swal.fire({
                type: 'info',
                title: '{{translate('messages.no_pending_orders')}}',
                text: '{{translate('messages.no_pending_orders_found')}}'
            });
            return;
        }

        Swal.fire({
            title: '{{translate('messages.are_you_sure')}}',
            text: `{{translate('messages.close_all_pending_earnings')}} (${allPendingOrders.length} {{translate('messages.orders')}})?`,
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '{{translate('messages.yes_close_all')}}',
            cancelButtonText: '{{translate('messages.cancel')}}'
        }).then((result) => {
            if (result.value === true || result.isConfirmed) {
                $.post({
                    url: '{{route('vendor.delivery-man.close-financial')}}',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        delivery_man_id: {{ $dm->id }},
                        order_ids: allPendingOrders
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    beforeSend: function () {
                        Swal.fire({
                            title: '{{translate('messages.processing')}}',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading()
                            }
                        });
                    },
                    success: function (data) {
                        Swal.fire({
                            type: 'success',
                            title: '{{translate('messages.success')}}',
                            text: data.message
                        }).then(() => {
                            location.reload();
                        });
                    },
                    error: function (xhr) {
                        let message = '{{translate('messages.something_went_wrong')}}';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            type: 'error',
                            title: '{{translate('messages.error')}}',
                            text: message
                        });
                    }
                });
            }
        });
    }
</script>
@endpush
