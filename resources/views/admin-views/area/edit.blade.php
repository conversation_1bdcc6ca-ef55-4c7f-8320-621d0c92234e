@extends('layouts.admin.app')

@section('title', translate('messages.edit_area'))

@push('css_or_js')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">{{translate('messages.edit_area')}}</h1>
                </div>

                <div class="col-sm-auto">
                    <a class="btn btn-outline-info" href="{{route('admin.business-settings.zone.area.index')}}">
                        <i class="tio-back-ui"></i> {{translate('messages.back')}}
                    </a>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row gx-2 gx-lg-3">
            <div class="col-sm-12 col-lg-12 mb-3 mb-lg-2">
                <!-- Card -->
                <div class="card">
                    <div class="card-body">
                        <form action="{{route('admin.business-settings.zone.area.update', $area->id)}}" method="post" id="area-form">
                            @csrf
                            @method('PUT')

                            @php($language = getWebConfig('language'))
                            @php($default_lang = 'default')

                            <ul class="nav nav-tabs mb-4">
                                <li class="nav-item">
                                    <a class="nav-link lang_link active" href="#" id="default-link">{{translate('messages.default')}}</a>
                                </li>
                                @foreach($language as $lang)
                                    <li class="nav-item">
                                        <a class="nav-link lang_link" href="#" id="{{$lang}}-link">{{strtoupper($lang)}}</a>
                                    </li>
                                @endforeach
                            </ul>

                            <div class="row">
                                <div class="col-12">
                                    <div class="lang_form" id="default-form">
                                        <div class="form-group">
                                            <label class="input-label" for="name">{{translate('messages.area_name')}} ({{translate('messages.default')}})</label>
                                            <input type="text" name="name[]" id="name" class="form-control"
                                                   placeholder="{{translate('messages.area_name')}}"
                                                   value="{{$area?->getRawOriginal('name')}}"
                                                   maxlength="191" required>
                                        </div>
                                        <input type="hidden" name="lang[]" value="default">
                                    </div>
                                    @foreach($language as $lang)
                                        <?php
                                            if(count($area['translations'])){
                                                $translate = [];
                                                foreach($area['translations'] as $t)
                                                {
                                                    if($t->locale == $lang && $t->key=="name"){
                                                        $translate[$lang]['name'] = $t->value;
                                                    }
                                                }
                                            }
                                        ?>
                                        <div class="lang_form d-none" id="{{$lang}}-form">
                                            <div class="form-group">
                                                <label class="input-label" for="name_{{$lang}}">{{translate('messages.area_name')}} ({{strtoupper($lang)}})</label>
                                                <input type="text" name="name[]" id="name_{{$lang}}" class="form-control"
                                                       placeholder="{{translate('messages.area_name')}}"
                                                       value="{{$translate[$lang]['name']??''}}"
                                                       maxlength="191">
                                            </div>
                                            <input type="hidden" name="lang[]" value="{{$lang}}">
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="input-label" for="zone_id">{{translate('messages.zone')}}</label>
                                        <select name="zone_id" id="zone_id" class="form-control js-select2-custom" required>
                                            <option value="">{{translate('messages.select_zone')}}</option>
                                            @foreach($zones as $zone)
                                                <option value="{{$zone->id}}" {{$area->zone_id == $zone->id ? 'selected' : ''}}>{{$zone->name}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="input-label" for="price">{{translate('messages.delivery_price')}}</label>
                                        <input type="number" name="price" id="price" class="form-control"
                                               placeholder="0.00" value="{{$area->price}}"
                                               min="0" step="0.01" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" name="status" id="status" value="1" {{$area->status ? 'checked' : ''}}>
                                            <label class="form-check-label" for="status">
                                                {{translate('messages.active')}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="btn--container justify-content-end">
                                <button type="reset" class="btn btn--reset">{{translate('messages.reset')}}</button>
                                <button type="submit" class="btn btn--primary">{{translate('messages.update')}}</button>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- End Card -->
            </div>
        </div>
    </div>
@endsection

@push('script_2')
    <script>
        $(document).ready(function() {
            $('.lang_link').click(function(e) {
                e.preventDefault();
                $('.lang_link').removeClass('active');
                $('.lang_form').addClass('d-none');
                $(this).addClass('active');

                let form_id = this.id;
                let lang = form_id.substring(0, form_id.length - 5);
                $('#' + lang + '-form').removeClass('d-none');
            });

            $('#area-form').on('submit', function(e) {
                e.preventDefault();

                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                $.ajax({
                    url: $(this).attr('action'),
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.href = '{{route("admin.business-settings.zone.area.index")}}';
                            }, 1000);
                        }
                    },
                    error: function(xhr) {
                        if (xhr.status === 422) {
                            let errors = xhr.responseJSON.errors;
                            $.each(errors, function(key, value) {
                                toastr.error(value[0]);
                            });
                        } else {
                            toastr.error('{{translate("messages.something_went_wrong")}}');
                        }
                    }
                });
            });
        });
    </script>
@endpush
