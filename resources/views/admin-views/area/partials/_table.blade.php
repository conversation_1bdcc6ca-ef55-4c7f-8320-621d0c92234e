@foreach($areas as $key => $area)
    <tr>
        <td>{{$key + $areas->firstItem()}}</td>
        <td>
            <span class="d-block font-size-sm text-body">
                {{$area['name']}}
            </span>
        </td>
        <td>
            <span class="badge badge-soft-info">
                {{$area->zone ? $area->zone->name : translate('messages.zone_deleted')}}
            </span>
        </td>
        <td>
            <span class="font-weight-bold">
                {{\App\CentralLogics\Helpers::format_currency($area['price'])}}
            </span>
        </td>
        <td>
            <label class="toggle-switch toggle-switch-sm" for="status-{{$area['id']}}">
                <input type="checkbox" class="toggle-switch-input dynamic-checkbox"
                       data-id="status-{{$area['id']}}"
                       data-type="status"
                       data-image-on='{{asset('/public/assets/admin/img/modal')}}/area-status-on.png'
                       data-image-off="{{asset('/public/assets/admin/img/modal')}}/area-status-off.png"
                       data-title-on="{{translate('Want_to_activate_this_area?')}}"
                       data-title-off="{{translate('Want_to_deactivate_this_area?')}}"
                       data-text-on="<p>{{translate('If_you_activate_this_area,_customers_can_select_it_when_adding_addresses.')}}</p>"
                       data-text-off="<p>{{translate('If_you_deactivate_this_area,_customers_will_NOT_be_able_to_select_it_when_adding_addresses.')}}</p>"
                       id="status-{{$area['id']}}" {{$area->status?'checked':''}}>
                <span class="toggle-switch-label">
                    <span class="toggle-switch-indicator"></span>
                </span>
            </label>
            <form action="{{route('admin.business-settings.zone.area.status',[$area['id'],$area->status?0:1])}}" method="get" id="status-{{$area['id']}}">
            </form>
        </td>
        <td>
            <div class="btn--container justify-content-center">
                <a class="btn action-btn btn--primary btn-outline-primary"
                   href="{{route('admin.business-settings.zone.area.edit',[$area['id']])}}" title="{{translate('messages.edit_area')}}">
                    <i class="tio-edit"></i>
                </a>
                <a class="btn action-btn btn--danger btn-outline-danger form-alert"
                   href="javascript:"
                   data-id="area-{{$area['id']}}"
                   data-message="{{translate('messages.Want_to_delete_this_area')}}"
                   title="{{translate('messages.delete_area')}}">
                    <i class="tio-delete-outlined"></i>
                </a>
                <form action="{{route('admin.business-settings.zone.area.destroy',[$area['id']])}}"
                      method="post" id="area-{{$area['id']}}">
                    @csrf @method('delete')
                </form>
            </div>
        </td>
    </tr>
@endforeach
