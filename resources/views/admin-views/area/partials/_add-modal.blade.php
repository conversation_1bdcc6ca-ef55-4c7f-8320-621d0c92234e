<!-- Modal -->
<div class="modal fade" id="add-area-modal" tabindex="-1" role="dialog" aria-labelledby="add-area-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-area-modal-label">{{translate('messages.add_new_area')}}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{route('admin.business-settings.zone.area.store')}}" method="post" id="add-area-form">
                @csrf
                <div class="modal-body">
                    @php($language = getWebConfig('language'))
                    @php($default_lang = 'default')

                    <ul class="nav nav-tabs mb-4">
                        <li class="nav-item">
                            <a class="nav-link lang_link active" href="#" id="default-link">{{translate('messages.default')}}</a>
                        </li>
                        @foreach($language as $lang)
                            <li class="nav-item">
                                <a class="nav-link lang_link" href="#" id="{{$lang}}-link">{{strtoupper($lang)}}</a>
                            </li>
                        @endforeach
                    </ul>

                    <div class="row">
                        <div class="col-12">
                            <div class="lang_form" id="default-form">
                                <div class="form-group">
                                    <label class="input-label" for="name">{{translate('messages.area_name')}} ({{translate('messages.default')}})</label>
                                    <input type="text" name="name[]" id="name" class="form-control" placeholder="{{translate('messages.area_name')}}" maxlength="191" required>
                                </div>
                                <input type="hidden" name="lang[]" value="default">
                            </div>
                            @foreach($language as $lang)
                                <div class="lang_form d-none" id="{{$lang}}-form">
                                    <div class="form-group">
                                        <label class="input-label" for="name_{{$lang}}">{{translate('messages.area_name')}} ({{strtoupper($lang)}})</label>
                                        <input type="text" name="name[]" id="name_{{$lang}}" class="form-control" placeholder="{{translate('messages.area_name')}}" maxlength="191">
                                    </div>
                                    <input type="hidden" name="lang[]" value="{{$lang}}">
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="input-label" for="zone_id">{{translate('messages.zone')}}</label>
                                <select name="zone_id" id="zone_id" class="form-control js-select2-custom" required>
                                    <option value="">{{translate('messages.select_zone')}}</option>
                                    @foreach($zones as $zone)
                                        <option value="{{$zone->id}}">{{$zone->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="input-label" for="price">{{translate('messages.delivery_price')}}</label>
                                <input type="number" name="price" id="price" class="form-control" placeholder="0.00" min="0" step="0.01" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" name="status" id="status" value="1" checked>
                                    <label class="form-check-label" for="status">
                                        {{translate('messages.active')}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{translate('messages.close')}}</button>
                    <button type="submit" class="btn btn-primary">{{translate('messages.save')}}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('.lang_link').click(function(e) {
            e.preventDefault();
            $('.lang_link').removeClass('active');
            $('.lang_form').addClass('d-none');
            $(this).addClass('active');

            let form_id = this.id;
            let lang = form_id.substring(0, form_id.length - 5);
            console.log(lang);
            $('#' + lang + '-form').removeClass('d-none');
            if (lang == 'default') {
                $(".from_part_2").removeClass('d-none');
            } else {
                $(".from_part_2").addClass('d-none');
            }
        });

        // لا حاجة لـ AJAX - سيتم إرسال الـ form بشكل طبيعي
    });
</script>
