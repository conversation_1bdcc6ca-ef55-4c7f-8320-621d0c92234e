@extends('layouts.admin.app')

@section('title', translate('messages.areas'))

@push('css_or_js')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@section('content')
    <div class="content container-fluid">
        <!-- Page Header -->
        <div class="page-header">
            <div class="row align-items-center">
                <div class="col-sm mb-2 mb-sm-0">
                    <h1 class="page-header-title">{{translate('messages.areas')}} <span class="badge badge-soft-dark ml-2">{{$areas->total()}}</span></h1>
                </div>
            </div>
        </div>
        <!-- End Page Header -->

        <div class="row gx-2 gx-lg-3">
            <div class="col-sm-12 col-lg-12 mb-3 mb-lg-2">
                <!-- Card -->
                <div class="card">
                    <!-- Header -->
                    <div class="card-header py-2 border-0">
                        <div class="search--button-wrapper">
                            <h5 class="card-title">{{translate('messages.area_list')}} <span class="badge badge-soft-dark ml-2" id="itemCount">{{$areas->total()}}</span></h5>
                            <form class="search-form">
                                <!-- Search -->
                                <div class="input--group input-group input-group-merge input-group-flush">
                                    <input id="datatableSearch_" type="search" name="search" class="form-control" placeholder="{{translate('messages.search_areas')}}" value="{{request()->get('search')}}" aria-label="{{translate('messages.search_areas')}}">
                                    <button type="submit" class="btn btn--secondary">
                                        <i class="tio-search"></i>
                                    </button>
                                </div>
                                <!-- End Search -->
                            </form>
                            <!-- Unfold -->
                            <div class="hs-unfold mr-2">
                                <select name="zone_id" class="form-control js-select2-custom" onchange="filter_zone()">
                                    <option value="all">{{translate('messages.all_zones')}}</option>
                                    @foreach($zones as $zone)
                                        <option value="{{$zone->id}}" {{request()->get('zone_id') == $zone->id ? 'selected' : ''}}>{{$zone->name}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <!-- End Unfold -->
                            <button type="button" class="btn btn--primary" data-toggle="modal" data-target="#add-area-modal">
                                <i class="tio-add-circle"></i>
                                {{translate('messages.add_new_area')}}
                            </button>
                        </div>
                    </div>
                    <!-- End Header -->

                    <!-- Table -->
                    <div class="table-responsive datatable-custom">
                        <table id="columnSearchDatatable"
                               class="table table-borderless table-thead-bordered table-nowrap table-align-middle card-table"
                               data-hs-datatables-options='{
                                 "order": [],
                                 "orderCellsTop": true
                               }'>
                            <thead class="thead-light">
                                <tr>
                                    <th>{{translate('messages.sl')}}</th>
                                    <th>{{translate('messages.area_name')}}</th>
                                    <th>{{translate('messages.zone')}}</th>
                                    <th>{{translate('messages.price')}}</th>
                                    <th>{{translate('messages.status')}}</th>
                                    <th class="text-center">{{translate('messages.action')}}</th>
                                </tr>
                            </thead>

                            <tbody id="table-div">
                                @include('admin-views.area.partials._table', ['areas' => $areas])
                            </tbody>
                        </table>
                    </div>
                    <!-- End Table -->

                    <!-- Footer -->
                    <div class="card-footer">
                        <!-- Pagination -->
                        {!! $areas->links() !!}
                        <!-- End Pagination -->
                    </div>
                    <!-- End Footer -->
                </div>
                <!-- End Card -->
            </div>
        </div>
    </div>

    <!-- Add Area Modal -->
    @include('admin-views.area.partials._add-modal')
    <!-- End Add Area Modal -->
@endsection

@push('script_2')
    <script>
        function filter_zone() {
            var zone_id = $('select[name="zone_id"]').val();
            var url = new URL(window.location.href);
            url.searchParams.set('zone_id', zone_id);
            window.location.href = url.href;
        }

        function status_change_alert(url, message, e) {
            e.preventDefault();
            Swal.fire({
                title: '{{translate("messages.are_you_sure")}}',
                text: message,
                type: 'warning',
                showCancelButton: true,
                cancelButtonColor: 'default',
                confirmButtonColor: '#FC6A57',
                cancelButtonText: '{{translate("messages.no")}}',
                confirmButtonText: '{{translate("messages.yes")}}',
                reverseButtons: true
            }).then((result) => {
                if (result.value) {
                    location.href = url;
                }
            })
        }

        function form_alert(id, message) {
            Swal.fire({
                title: '{{translate("messages.are_you_sure")}}',
                text: message,
                type: 'warning',
                showCancelButton: true,
                cancelButtonColor: 'default',
                confirmButtonColor: '#FC6A57',
                cancelButtonText: '{{translate("messages.no")}}',
                confirmButtonText: '{{translate("messages.yes")}}',
                reverseButtons: true
            }).then((result) => {
                if (result.value) {
                    $('#' + id).submit()
                }
            })
        }
    </script>
@endpush
