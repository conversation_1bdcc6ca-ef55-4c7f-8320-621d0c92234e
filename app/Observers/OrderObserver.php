<?php

namespace App\Observers;

use App\Models\Order;
use App\Models\OrderReference;
use App\Services\ExternalInvoiceService;

class OrderObserver
{
    /**
     * Handle the Order "created" event.
     */
    public function created(Order $order): void
    {
        $OrderReference = new OrderReference();
        $OrderReference->order_id = $order->id;
        $OrderReference->save();
    }

    /**
     * Handle the Order "updated" event.
     */
    public function updated(Order $order): void
    {
        // التحقق من تغيير حالة الطلب
        if ($order->isDirty('order_status')) {
            $oldStatus = $order->getOriginal('order_status');
            $newStatus = $order->order_status;

            // إرسال إلى النظام الخارجي عند تغيير الحالة من معلق إلى أي حالة أخرى غير الإلغاء
            if ($oldStatus === 'pending' &&
                $newStatus !== 'pending' &&
                $newStatus !== 'canceled' &&
                $newStatus !== 'failed') {

                try {
                    $externalService = new ExternalInvoiceService();
                    $externalService->sendOrderToExternal($order);
                } catch (\Exception $e) {
                    \Log::error("Failed to send order {$order->id} to external system: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Handle the Order "deleted" event.
     */
    public function deleted(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     */
    public function restored(Order $order): void
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     */
    public function forceDeleted(Order $order): void
    {
        //
    }
}
