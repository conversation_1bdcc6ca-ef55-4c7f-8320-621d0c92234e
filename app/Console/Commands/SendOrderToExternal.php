<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Services\ExternalInvoiceService;

class SendOrderToExternal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:send-external {order_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send a specific order to external invoice system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orderId = $this->argument('order_id');

        $order = Order::with(['customer', 'details'])->find($orderId);

        if (!$order) {
            $this->error("Order with ID {$orderId} not found.");
            return 1;
        }

        $this->info("Sending order {$orderId} to external system...");

        $externalService = new ExternalInvoiceService();
        $result = $externalService->sendOrderToExternal($order);

        if ($result) {
            $this->info("Order {$orderId} sent successfully!");
            if ($order->external_guid) {
                $this->info("External GUID: {$order->external_guid}");
            }
        } else {
            $this->error("Failed to send order {$orderId} to external system.");
            return 1;
        }

        return 0;
    }
}
