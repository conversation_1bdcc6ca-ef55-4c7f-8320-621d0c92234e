<?php

namespace App\Http\Controllers\Admin;

use App\Contracts\Repositories\TranslationRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Models\Area;
use App\Models\Zone;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AreaController extends Controller
{
    public function __construct(
        protected TranslationRepositoryInterface $translationRepo
    ) {
    }

    /**
     * Display a listing of the areas.
     */
    public function index(Request $request)
    {
        $key = explode(' ', $request['search']);
        $zones = Zone::active()->get();

        $areas = Area::with(['zone', 'translations'])
            ->when($request->has('zone_id') && $request->zone_id != 'all', function ($query) use ($request) {
                return $query->where('zone_id', $request->zone_id);
            })
            ->when(isset($key), function ($query) use ($key) {
                return $query->where(function ($q) use ($key) {
                    foreach ($key as $value) {
                        $q->orWhere('name', 'like', "%{$value}%");
                    }
                });
            })
            ->latest()
            ->paginate(config('default_pagination'));

        return view('admin-views.area.index', compact('areas', 'zones'));
    }

    /**
     * Store a newly created area in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|array',
            'name.0' => 'required|string|max:191',
            'price' => 'required|numeric|min:0',
            'zone_id' => 'required|exists:zones,id',
            'status' => 'boolean',
        ], [
            'name.0.required' => translate('messages.Name is required'),
            'price.required' => translate('messages.Price is required'),
            'zone_id.required' => translate('messages.Zone is required'),
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $area = Area::create([
            'name' => $request->name[0],
            'price' => $request->price,
            'zone_id' => $request->zone_id,
            'status' => $request->status ?? 1,
        ]);

        $this->translationRepo->addByModel(request: $request, model: $area, modelPath: 'App\Models\Area', attribute: 'name');

        Toastr::success(translate('messages.area_added_successfully'));

        return redirect()->back();
    }

    /**
     * Show the form for editing the specified area.
     */
    public function edit($id)
    {
        $area = Area::withoutGlobalScope('translate')->with('translations')->findOrFail($id);
        $zones = Zone::active()->get();
        $language = getWebConfig('language');

        return view('admin-views.area.edit', compact('area', 'zones', 'language'));
    }

    /**
     * Update the specified area in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|array',
            'name.0' => 'required|string|max:191',
            'price' => 'required|numeric|min:0',
            'zone_id' => 'required|exists:zones,id',
            'status' => 'boolean',
        ], [
            'name.0.required' => translate('messages.Name is required'),
            'price.required' => translate('messages.Price is required'),
            'zone_id.required' => translate('messages.Zone is required'),
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $area = Area::findOrFail($id);
        $area->update([
            'name' => $request->name[0],
            'price' => $request->price,
            'zone_id' => $request->zone_id,
            'status' => $request->status ?? 1,
        ]);

        $this->translationRepo->updateByModel(request: $request, model: $area, modelPath: 'App\Models\Area', attribute: 'name');

        Toastr::success(translate('messages.area_updated_successfully'));

        return response()->json(['success' => true, 'message' => translate('messages.area_updated_successfully')]);
    }

    /**
     * Update the status of the specified area.
     */
    public function status(Request $request)
    {
        $area = Area::findOrFail($request->id);
        $area->status = $request->status;
        $area->save();

        Toastr::success(translate('messages.area_status_updated_successfully'));

        return response()->json(['success' => true, 'message' => translate('messages.area_status_updated_successfully')]);
    }

    /**
     * Remove the specified area from storage.
     */
    public function destroy($id)
    {
        $area = Area::findOrFail($id);

        // Check if area is being used in customer addresses
        if ($area->customerAddresses()->count() > 0) {
            Toastr::error(translate('messages.area_is_being_used_cannot_delete'));

            return response()->json(['success' => false, 'message' => translate('messages.area_is_being_used_cannot_delete')]);
        }

        $area->translations()->delete();
        $area->delete();

        Toastr::success(translate('messages.area_deleted_successfully'));

        return response()->json(['success' => true, 'message' => translate('messages.area_deleted_successfully')]);
    }

    /**
     * Get areas by zone for AJAX requests.
     */
    public function getAreasByZone(Request $request)
    {
        $areas = Area::active()
            ->where('zone_id', $request->zone_id)
            ->get(['id', 'name', 'price']);

        return response()->json($areas);
    }
}
