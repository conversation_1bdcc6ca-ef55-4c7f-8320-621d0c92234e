<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Area;
use App\Models\Zone;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AreaController extends Controller
{
    /**
     * Get all active areas.
     */
    public function index(): JsonResponse
    {
        try {
            $areas = Area::active()
                ->with(['zone:id,name', 'zone.translations', 'translations'])
                ->get(['id', 'name', 'price', 'zone_id']);

            return response()->json([
                'status' => true,
                'message' => 'Areas retrieved successfully',
                'data' => $areas,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve areas',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get areas by zone ID.
     */
    public function getByZone(Request $request, $zoneId): JsonResponse
    {
        try {
            // Validate zone exists
            $zone = Zone::find($zoneId);
            if (!$zone) {
                return response()->json([
                    'status' => false,
                    'message' => 'Zone not found',
                ], 404);
            }

            $areas = Area::active()
                ->with(['translations'])
                ->where('zone_id', $zoneId)
                ->get(['id', 'name', 'price', 'zone_id']);

            return response()->json([
                'status' => true,
                'message' => 'Areas retrieved successfully',
                'data' => $areas,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve areas',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific area details.
     */
    public function show($id): JsonResponse
    {
        try {
            $area = Area::active()
                ->with(['zone:id,name', 'zone.translations', 'translations'])
                ->find($id);

            if (!$area) {
                return response()->json([
                    'status' => false,
                    'message' => 'Area not found',
                ], 404);
            }

            return response()->json([
                'status' => true,
                'message' => 'Area retrieved successfully',
                'data' => $area,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve area',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Search areas by name.
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $searchTerm = $request->get('search', '');
            $zoneId = $request->get('zone_id');

            $query = Area::active();

            if ($searchTerm) {
                $query->where('name', 'like', "%{$searchTerm}%");
            }

            if ($zoneId) {
                $query->where('zone_id', $zoneId);
            }

            $areas = $query->with(['zone:id,name', 'zone.translations', 'translations'])
                ->get(['id', 'name', 'price', 'zone_id']);

            return response()->json([
                'status' => true,
                'message' => 'Areas retrieved successfully',
                'data' => $areas,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to search areas',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
