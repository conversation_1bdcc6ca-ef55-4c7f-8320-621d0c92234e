<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\DeliveryMan;
use App\Models\DMReview;
use App\Models\Zone;
use App\Models\Order;
use App\Models\OrderTransaction;
use App\Models\UserInfo;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\DisbursementDetails;
use App\Exports\DeliveryManListExport;
use App\Exports\DeliveryManReviewExport;
use App\Exports\SingleDeliveryManReviewExport;
use App\Exports\DeliveryManEarningExport;
use App\Exports\DisbursementHistoryExport;
use Brian2694\Toastr\Facades\Toastr;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use App\CentralLogics\Helpers;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use Maatwebsite\Excel\Facades\Excel;

class DeliveryManController extends Controller
{
    public function index()
    {
        $zones = Zone::active()->get();
        return view('vendor-views.delivery-man.index', compact('zones'));
    }

    public function list(Request $request)
    {
        $key = explode(' ', $request['search']);
        $delivery_men = DeliveryMan::where('store_id', Helpers::get_store_id())
                 ->when( isset($key) , function($query) use($key){
                    $query->where(function ($q) use ($key) {
                        foreach ($key as $value) {
                            $q->orWhere('f_name', 'like', "%{$value}%")
                                ->orWhere('l_name', 'like', "%{$value}%")
                                ->orWhere('email', 'like', "%{$value}%")
                                ->orWhere('phone', 'like', "%{$value}%")
                                ->orWhere('identity_number', 'like', "%{$value}%");
                        }
                    });
                }
        )->latest()->paginate(config('default_pagination'));
        return view('vendor-views.delivery-man.list', compact('delivery_men'));
    }



    public function reviews_list(){
        $reviews=DMReview::with(['delivery_man','customer'])->latest()->paginate(config('default_pagination'));
        return view('vendor-views.delivery-man.reviews-list',compact('reviews'));
    }

    public function preview($id, $tab='info')
    {
        $dm = DeliveryMan::with(['reviews'])->where('store_id', Helpers::get_store_id())->where(['id' => $id])->first();

        if (!$dm) {
            Toastr::error(translate('messages.delivery_man_not_found'));
            return back();
        }

        if($tab == 'info')
        {
            $reviews=DMReview::where(['delivery_man_id'=>$id])->latest()->paginate(config('default_pagination'));
            return view('vendor-views.delivery-man.view.info', compact('dm', 'reviews'));
        }
        else if($tab == 'transaction')
        {
            $digital_transaction = OrderTransaction::where('delivery_man_id', $id)->latest()->paginate(config('default_pagination'));

            // Get all orders for this delivery man
            $orders = Order::where('delivery_man_id', $id)
                ->where('store_id', Helpers::get_store_id())
                ->with(['customer', 'store'])
                ->latest()
                ->paginate(config('default_pagination'));

            // Get pending earning orders count
            $pending_earning_orders_count = Order::where('delivery_man_id', $id)
                ->where('store_id', Helpers::get_store_id())
                ->where('close_financial_dm', false)
                ->where('order_status', 'delivered')
                ->where('payment_method', 'digital_payment')
                ->count();

            return view('vendor-views.delivery-man.view.transaction', compact('dm', 'digital_transaction', 'orders', 'pending_earning_orders_count'));
        }

        // Default to info tab if invalid tab provided
        $reviews=DMReview::where(['delivery_man_id'=>$id])->latest()->paginate(config('default_pagination'));
        return view('vendor-views.delivery-man.view.info', compact('dm', 'reviews'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'f_name' => 'required|max:100',
            'l_name' => 'nullable|max:100',
            'identity_number' => 'required|max:30',
            'email' => 'required|unique:delivery_men',
            'phone' => 'required|regex:/^([0-9\s\-\+\(\)]*)$/|min:10|unique:delivery_men',
            'password' => ['required', Password::min(8)->mixedCase()->letters()->numbers()->symbols()->uncompromised()],
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => Helpers::error_processor($validator)]);
        }

        if ($request->has('image')) {
            $image_name = Helpers::upload('delivery-man/', 'png', $request->file('image'));
        } else {
            $image_name = 'def.png';
        }

        $id_img_names = [];
        if (!empty($request->file('identity_image'))) {
            foreach ($request->identity_image as $img) {
                $identity_image = Helpers::upload('delivery-man/', 'png', $img);
                array_push($id_img_names, ['img'=>$identity_image, 'storage'=> Helpers::getDisk()]);
            }
            $identity_image = json_encode($id_img_names);
        } else {
            $identity_image = json_encode([]);
        }

        // Get store info to set zone_id
        $store = \App\Models\Store::find(Helpers::get_store_id());

        $dm = New DeliveryMan();
        $dm->f_name = $request->f_name;
        $dm->l_name = $request->l_name;
        $dm->email = $request->email;
        $dm->phone = $request->phone;
        $dm->identity_number = $request->identity_number;
        $dm->identity_type = $request->identity_type;
        $dm->store_id = Helpers::get_store_id();
        $dm->zone_id = $store ? $store->zone_id : $request->zone_id;
        $dm->identity_image = $identity_image;
        $dm->image = $image_name;
        $dm->active = 0;
        $dm->earning = 0;
        $dm->type = 'store_wise';
        $dm->password = bcrypt($request->password);
        $dm->save();

        return response()->json(['message' => translate('messages.deliveryman_added_successfully')], 200);

    }

    public function edit($id)
    {
        $delivery_man = DeliveryMan::find($id);
        return view('vendor-views.delivery-man.edit', compact('delivery_man'));
    }

    public function status(Request $request)
    {
        $delivery_man = DeliveryMan::find($request->id);
        $delivery_man->status = $request->status;

        try
        {
            if($request->status == 0)
            {   $delivery_man->auth_token = null;
                if(isset($delivery_man->fcm_token) && Helpers::getNotificationStatusData('deliveryman','deliveryman_account_block','push_notification_status') )
                {
                    $data = [
                        'title' => translate('messages.suspended'),
                        'description' => translate('messages.your_account_has_been_suspended'),
                        'order_id' => '',
                        'image' => '',
                        'type'=> 'block'
                    ];
                    Helpers::send_push_notif_to_device($delivery_man->fcm_token, $data);

                    DB::table('user_notifications')->insert([
                        'data'=> json_encode($data),
                        'delivery_man_id'=>$delivery_man->id,
                        'created_at'=>now(),
                        'updated_at'=>now()
                    ]);
                }

            } else{
                if( Helpers::getNotificationStatusData('deliveryman','deliveryman_account_unblock','push_notification_status') && isset($delivery_man->fcm_token))
                {
                    $data = [
                        'title' => translate('messages.Account_activation'),
                        'description' => translate('messages.your_account_has_been_activated'),
                        'order_id' => '',
                        'image' => '',
                        'type'=> 'unblock'
                    ];
                    Helpers::send_push_notif_to_device($delivery_man->fcm_token, $data);

                    DB::table('user_notifications')->insert([
                        'data'=> json_encode($data),
                        'delivery_man_id'=>$delivery_man->id,
                        'created_at'=>now(),
                        'updated_at'=>now()
                    ]);
                }
            }

        }
        catch (\Exception $e) {
            Toastr::warning(translate('messages.push_notification_faild'));
        }

        $delivery_man->save();

        Toastr::success(translate('messages.deliveryman_status_updated'));
        return back();
    }

    public function earning(Request $request)
    {
        $delivery_man = DeliveryMan::find($request->id);
        $delivery_man->earning = $request->status;

        $delivery_man->save();

        Toastr::success(translate('messages.deliveryman_type_updated'));
        return back();
    }

    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'f_name' => 'required|max:100',
            'l_name' => 'nullable|max:100',
            'identity_number' => 'required|max:30',
            'email' => 'required|unique:delivery_men,email,'.$id,
            'phone' => 'required|regex:/^([0-9\s\-\+\(\)]*)$/|min:10|unique:delivery_men,phone,'.$id,
            'password' => ['nullable', Password::min(8)->mixedCase()->letters()->numbers()->symbols()->uncompromised()],
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => Helpers::error_processor($validator)]);
        }

        $delivery_man = DeliveryMan::find($id);

        if ($request->has('image')) {
            $image_name = Helpers::update('delivery-man/', $delivery_man->image, 'png', $request->file('image'));
        } else {
            $image_name = $delivery_man['image'];
        }

        if ($request->has('identity_image')){
            foreach (json_decode($delivery_man['identity_image'], true) as $img) {

                Helpers::check_and_delete('delivery-man/' , $img);

            }
            $img_keeper = [];
            foreach ($request->identity_image as $img) {
                $identity_image = Helpers::upload('delivery-man/', 'png', $img);
                array_push($img_keeper, ['img'=>$identity_image, 'storage'=> Helpers::getDisk()]);
            }
            $identity_image = json_encode($img_keeper);
        } else {
            $identity_image = $delivery_man['identity_image'];
        }

        $delivery_man->f_name = $request->f_name;
        $delivery_man->l_name = $request->l_name;
        $delivery_man->email = $request->email;
        $delivery_man->phone = $request->phone;
        $delivery_man->identity_number = $request->identity_number;
        $delivery_man->identity_type = $request->identity_type;
        $delivery_man->identity_image = $identity_image;
        $delivery_man->image = $image_name;

        $delivery_man->password = strlen($request->password)>1?bcrypt($request->password):$delivery_man['password'];
        $delivery_man->save();

        if($delivery_man->userinfo) {
            $userinfo = $delivery_man->userinfo;
            $userinfo->f_name = $request->f_name;
            $userinfo->l_name = $request->l_name;
            $userinfo->email = $request->email;
            $userinfo->image = $image_name;
            $userinfo->save();
        }

        return response()->json(['message' => translate('messages.deliveryman_updated_successfully')], 200);

    }

    public function delete(Request $request)
    {
        $delivery_man = DeliveryMan::find($request->id);

        Helpers::check_and_delete('delivery-man/' , $delivery_man['image']);


        foreach (json_decode($delivery_man['identity_image'], true) as $img) {

            Helpers::check_and_delete('delivery-man/' , $img);

        }
        if($delivery_man->userinfo){

            $delivery_man->userinfo->delete();
        }
        $delivery_man->delete();
        Toastr::success(translate('messages.deliveryman_deleted_successfully'));
        return back();
    }

    public function get_deliverymen(Request $request){
        $key = explode(' ', $request->q);
        $zone_ids = isset($request->zone_ids)?(count($request->zone_ids)>0?$request->zone_ids:[]):0;
        $data=DeliveryMan::when($zone_ids, function($query) use($zone_ids){
            return $query->whereIn('zone_id', $zone_ids);
        })
        ->when($request->earning, function($query){
            return $query->earning();
        })
        ->where(function ($q) use ($key) {
            foreach ($key as $value) {
                $q->orWhere('f_name', 'like', "%{$value}%")
                    ->orWhere('l_name', 'like', "%{$value}%")
                    ->orWhere('email', 'like', "%{$value}%")
                    ->orWhere('phone', 'like', "%{$value}%")
                    ->orWhere('identity_number', 'like', "%{$value}%");
            }
        })->where('store_id', Helpers::get_store_id())->limit(8)->get(['id',DB::raw('CONCAT(f_name, " ", l_name) as text')]);
        return response()->json($data);
    }

    public function get_account_data(DeliveryMan $deliveryman)
    {
        $wallet = $deliveryman->wallet;
        $cash_in_hand = 0;
        $balance = 0;

        if($wallet)
        {
            $cash_in_hand = $wallet->collected_cash;
            $balance = $wallet->total_earning - $wallet->total_withdrawn - $wallet->pending_withdraw;
        }
        return response()->json(['cash_in_hand'=>$cash_in_hand, 'earning_balance'=>$balance], 200);

    }


    public function transaction_search(Request $request){
        $key = explode(' ', $request['search']);
        $digital_transaction=OrderTransaction::where(function ($q) use ($key) {
            foreach ($key as $value) {
                $q->orWhere('order_id', 'like', "%{$value}%");
            }
        })->where('delivery_man_id', $request->dm_id)->get();
        return response()->json([
            'view'=>view('vendor-views.delivery-man.partials._transation',compact('digital_transaction'))->render(),
            'count'=>$digital_transaction->count()
        ]);
    }

    public function orders_search(Request $request){
        $key = explode(' ', $request['search']);
        $orders = Order::where('delivery_man_id', $request->dm_id)
            ->where('store_id', Helpers::get_store_id())
            ->where(function ($q) use ($key) {
                foreach ($key as $value) {
                    $q->orWhere('id', 'like', "%{$value}%")
                      ->orWhereHas('customer', function($query) use ($value) {
                          $query->where('f_name', 'like', "%{$value}%")
                                ->orWhere('l_name', 'like', "%{$value}%")
                                ->orWhere('phone', 'like', "%{$value}%");
                      });
                }
            })
            ->with(['customer', 'store'])
            ->latest()
            ->get();

        return response()->json([
            'view'=>view('vendor-views.delivery-man.partials._orders_table',compact('orders'))->render()
        ]);
    }

    /**
     * Export delivery men list
     */
    public function export(Request $request)
    {
        $delivery_men = DeliveryMan::where('store_id', Helpers::get_store_id())
            ->when($request['search'], function($query) use($request){
                $key = explode(' ', $request['search']);
                $query->where(function ($q) use ($key) {
                    foreach ($key as $value) {
                        $q->orWhere('f_name', 'like', "%{$value}%")
                            ->orWhere('l_name', 'like', "%{$value}%")
                            ->orWhere('email', 'like', "%{$value}%")
                            ->orWhere('phone', 'like', "%{$value}%")
                            ->orWhere('identity_number', 'like', "%{$value}%");
                    }
                });
            })
            ->latest()->get();

        $data = [
            'delivery_men' => $delivery_men,
            'search' => $request->search ?? null,
        ];

        if ($request['type'] == 'excel') {
            return Excel::download(new DeliveryManListExport($data), 'DeliveryMen.xlsx');
        }
        return Excel::download(new DeliveryManListExport($data), 'DeliveryMen.csv');
    }

    /**
     * Export delivery man reviews
     */
    public function reviews_export(Request $request)
    {
        $reviews = DMReview::with(['delivery_man', 'customer'])
            ->whereHas('delivery_man', function($query) {
                $query->where('store_id', Helpers::get_store_id());
            })
            ->when($request['search'], function($query) use($request){
                $key = explode(' ', $request['search']);
                $query->where(function ($q) use ($key) {
                    foreach ($key as $value) {
                        $q->orWhereHas('delivery_man', function($query) use($value) {
                            $query->where('f_name', 'like', "%{$value}%")
                                ->orWhere('l_name', 'like', "%{$value}%");
                        });
                    }
                });
            })
            ->latest()->get();

        $data = [
            'reviews' => $reviews,
            'search' => $request->search ?? null,
        ];

        if ($request['type'] == 'excel') {
            return Excel::download(new DeliveryManReviewExport($data), 'DeliveryManReviews.xlsx');
        }
        return Excel::download(new DeliveryManReviewExport($data), 'DeliveryManReviews.csv');
    }

    /**
     * Update review status
     */
    public function update_review_status(Request $request)
    {
        $review = DMReview::whereHas('delivery_man', function($query) {
            $query->where('store_id', Helpers::get_store_id());
        })->find($request->id);

        if ($review) {
            $review->status = $request->status;
            $review->save();
            Toastr::success(translate('messages.review_visibility_updated'));
        } else {
            Toastr::error(translate('messages.review_not_found'));
        }

        return back();
    }

    /**
     * Export single delivery man reviews
     */
    public function single_reviews_export($id, $type)
    {
        // Check if delivery man belongs to current store
        $delivery_man = DeliveryMan::where('store_id', Helpers::get_store_id())
            ->where('id', $id)
            ->first();

        if (!$delivery_man) {
            Toastr::error(translate('messages.delivery_man_not_found'));
            return back();
        }

        $reviews = DMReview::with(['delivery_man', 'customer'])
            ->where('delivery_man_id', $id)
            ->latest()->get();

        $data = [
            'reviews' => $reviews,
            'delivery_man' => $delivery_man,
        ];

        if ($type == 'excel') {
            return Excel::download(new SingleDeliveryManReviewExport($data), 'DeliveryMan_' . $delivery_man->f_name . '_Reviews.xlsx');
        }
        return Excel::download(new SingleDeliveryManReviewExport($data), 'DeliveryMan_' . $delivery_man->f_name . '_Reviews.csv');
    }

    /**
     * Export delivery man earnings
     */
    public function earnings_export($id, $type)
    {
        // Check if delivery man belongs to current store
        $delivery_man = DeliveryMan::where('store_id', Helpers::get_store_id())
            ->where('id', $id)
            ->first();

        if (!$delivery_man) {
            Toastr::error(translate('messages.delivery_man_not_found'));
            return back();
        }

        $transactions = OrderTransaction::where('delivery_man_id', $id)
            ->latest()->get();

        $data = [
            'transactions' => $transactions,
            'delivery_man' => $delivery_man,
        ];

        if ($type == 'excel') {
            return Excel::download(new DeliveryManEarningExport($data), 'DeliveryMan_' . $delivery_man->f_name . '_Earnings.xlsx');
        }
        return Excel::download(new DeliveryManEarningExport($data), 'DeliveryMan_' . $delivery_man->f_name . '_Earnings.csv');
    }

    /**
     * Export delivery man disbursement
     */
    public function disbursement_export($id, $type)
    {
        // Check if delivery man belongs to current store
        $delivery_man = DeliveryMan::where('store_id', Helpers::get_store_id())
            ->where('id', $id)
            ->first();

        if (!$delivery_man) {
            Toastr::error(translate('messages.delivery_man_not_found'));
            return back();
        }

        $disbursements = DisbursementDetails::where('delivery_man_id', $id)
            ->latest()->get();

        $data = [
            'disbursements' => $disbursements,
            'delivery_man' => $delivery_man,
        ];

        if ($type == 'excel') {
            return Excel::download(new DisbursementHistoryExport($data), 'DeliveryMan_' . $delivery_man->f_name . '_Disbursements.xlsx');
        }
        return Excel::download(new DisbursementHistoryExport($data), 'DeliveryMan_' . $delivery_man->f_name . '_Disbursements.csv');
    }

    /**
     * Close financial for delivery man orders
     */
    public function close_financial(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'delivery_man_id' => 'required|integer',
                'order_ids' => 'required|array',
                'order_ids.*' => 'integer'
            ]);

            if ($validator->fails()) {
                Toastr::error(translate('messages.invalid_data'));
                return back();
            }

            // Check if delivery man belongs to current store
            $delivery_man = DeliveryMan::where('store_id', Helpers::get_store_id())
                ->where('id', $request->delivery_man_id)
                ->first();

            if (!$delivery_man) {
                Toastr::error(translate('messages.delivery_man_not_found'));
                return back();
            }

            // Log the request for debugging
            \Log::info('Close financial request', [
                'delivery_man_id' => $request->delivery_man_id,
                'order_ids' => $request->order_ids,
                'store_id' => Helpers::get_store_id()
            ]);

            // Update orders
            $updated = Order::whereIn('id', $request->order_ids)
                ->where('delivery_man_id', $request->delivery_man_id)
                ->where('store_id', Helpers::get_store_id())
                ->where('close_financial_dm', false)
                ->where('order_status', 'delivered')
                ->where('payment_method', 'digital_payment')
                ->update(['close_financial_dm' => true]);

            \Log::info('Close financial result', ['updated_count' => $updated]);

            if ($updated > 0) {
                Toastr::success(translate('messages.financial_status_updated_successfully'));
                return back();
            } else {
                Toastr::error(translate('messages.no_orders_updated'));
                return back();
            }
        } catch (\Exception $e) {
            \Log::error('Close financial error: ' . $e->getMessage());
            Toastr::error('خطأ في النظام: ' . $e->getMessage());
            return back();
        }
    }

}
