<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExternalInvoiceService
{
    private $apiUrl = 'https://albayan.live/ApiWithAmeen/public/api/HemamAddNewInvoice';
    
    // Default GUIDs - يمكن تخصيصها حسب النظام
    private $defaultTypeGUID = '1ACAEF7F-84B5-487D-929F-624F95283B81';
    private $defaultStoreGUID = 'b34050de-935f-4230-bd93-619d395c5268';
    private $defaultCustAccGUID = '814d7163-0729-481e-9f44-adefcdd7508b';
    private $defaultCustGUID = '00000000-0000-0000-0000-000000000000';
    private $defaultCostGUID = '00000000-0000-0000-0000-000000000000';

    /**
     * إرسال الطلب إلى النظام الخارجي
     */
    public function sendOrderToExternal(Order $order)
    {
        try {
            // التحقق من أن الطلب لم يتم إرساله من قبل
            if ($order->external_guid) {
                Log::info("Order {$order->id} already sent to external system with GUID: {$order->external_guid}");
                return true;
            }

            // إعداد بيانات الفاتورة
            $invoiceData = $this->prepareInvoiceData($order);
            
            Log::info("Sending order {$order->id} to external API", $invoiceData);

            // إرسال الطلب
            $response = Http::timeout(30)->post($this->apiUrl, $invoiceData);

            if ($response->successful()) {
                $responseData = $response->json();
                
                // حفظ الـ GUID المُرجع من النظام الخارجي
                if (isset($responseData['GUID']) || isset($responseData['guid'])) {
                    $guid = $responseData['GUID'] ?? $responseData['guid'];
                    $order->update([
                        'external_guid' => $guid,
                        'external_sent_at' => now()
                    ]);
                    
                    Log::info("Order {$order->id} sent successfully with GUID: {$guid}");
                    return true;
                } else {
                    Log::warning("Order {$order->id} sent but no GUID returned", $responseData);
                    $order->update(['external_sent_at' => now()]);
                    return true;
                }
            } else {
                Log::error("Failed to send order {$order->id} to external API", [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Exception while sending order {$order->id} to external API: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إعداد بيانات الفاتورة للإرسال
     */
    private function prepareInvoiceData(Order $order)
    {
        // حساب المجاميع
        $subtotal = $order->order_amount - $order->delivery_charge;
        $vat = 0; // يمكن حسابها حسب النظام
        $totalDiscount = $order->coupon_discount_amount ?? 0;
        $totalExtra = $order->delivery_charge ?? 0;

        // تحديد نوع الدفع
        $payType = $order->payment_method === 'cash_on_delivery' ? 0 : 1;

        // إعداد تفاصيل المواد
        $details = [];
        $orderDetails = $order->details ?? [];
        
        foreach ($orderDetails as $index => $detail) {
            $details[] = [
                "Qty" => $detail->quantity ?? 1,
                "Price" => $detail->price ?? 0,
                "MatGUID" => $this->getProductGUID($detail->food_id ?? 0),
                "StoreGUID" => $this->defaultStoreGUID,
                "CostGUID" => $this->defaultCostGUID,
                "VAT" => 0,
                "Discount" => 0,
                "Extra" => 0
            ];
        }

        // إذا لم توجد تفاصيل، أضف عنصر افتراضي
        if (empty($details)) {
            $details[] = [
                "Qty" => 1,
                "Price" => $subtotal,
                "MatGUID" => $this->getDefaultProductGUID(),
                "StoreGUID" => $this->defaultStoreGUID,
                "CostGUID" => $this->defaultCostGUID,
                "VAT" => 0,
                "Discount" => 0,
                "Extra" => 0
            ];
        }

        return [
            "Total" => $subtotal,
            "VAT" => $vat,
            "TotalDisc" => $totalDiscount,
            "TotalExtra" => $totalExtra,
            "PayType" => $payType,
            "Notes" => "Order #{$order->id} - {$order->customer->f_name} {$order->customer->l_name}",
            "TypeGUID" => $this->defaultTypeGUID,
            "CustGUID" => $this->getCustomerGUID($order->user_id),
            "StoreGUID" => $this->defaultStoreGUID,
            "CustAccGUID" => $this->defaultCustAccGUID,
            "CostGUID" => $this->defaultCostGUID,
            "Details" => $details
        ];
    }

    /**
     * الحصول على GUID المنتج
     */
    private function getProductGUID($productId)
    {
        // يمكن إضافة جدول mapping للمنتجات
        // أو استخدام GUID افتراضي
        return 'be6969bf-3077-4bfe-bf1c-a5e54a07d195'; // GUID افتراضي
    }

    /**
     * الحصول على GUID المنتج الافتراضي
     */
    private function getDefaultProductGUID()
    {
        return 'be6969bf-3077-4bfe-bf1c-a5e54a07d195';
    }

    /**
     * الحصول على GUID العميل
     */
    private function getCustomerGUID($customerId)
    {
        // يمكن إضافة جدول mapping للعملاء
        // أو استخدام GUID افتراضي للعملاء النقديين
        return $this->defaultCustGUID;
    }
}
