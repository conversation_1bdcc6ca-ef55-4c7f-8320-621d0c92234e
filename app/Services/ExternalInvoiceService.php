<?php

namespace App\Services;

use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ExternalInvoiceService
{
    private $apiUrl = 'https://albayan.live/ApiWithAmeen/public/api/HemamAddNewInvoice';

    // Default GUIDs - محدثة حسب الاستجابة من الـ API
    private $defaultTypeGUID = 'F1F015E6-CEDC-4F23-9E8F-42362E23C35F'; // من الاستجابة
    private $defaultStoreGUID = '8F4E1F46-71F0-48D5-AE0C-E8CC709025D0'; // من الاستجابة
    private $defaultCustAccGUID = 'F852742D-0437-48EC-819B-60853F47639E'; // من الاستجابة
    private $defaultCustGUID = '00000000-0000-0000-0000-000000000000';
    private $defaultCostGUID = '00000000-0000-0000-0000-000000000000';

    /**
     * إرسال الطلب إلى النظام الخارجي
     */
    public function sendOrderToExternal(Order $order)
    {
        try {
            // التحقق من أن الطلب لم يتم إرساله من قبل
            if ($order->external_guid) {
                Log::info("Order {$order->id} already sent to external system with GUID: {$order->external_guid}");
                return true;
            }

            // إعداد بيانات الفاتورة
            $invoiceData = $this->prepareInvoiceData($order);

            Log::info("Sending order {$order->id} to external API", $invoiceData);

            // إرسال الطلب
            $response = Http::timeout(30)->post($this->apiUrl, $invoiceData);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info("External API Response for order {$order->id}", $responseData);

                // معالجة الاستجابة - قد تكون array أو object
                $guid = null;

                if (is_array($responseData) && !empty($responseData)) {
                    // إذا كانت الاستجابة array، أخذ GUID من العنصر الأول
                    $firstItem = $responseData[0];
                    if (isset($firstItem['GUID'])) {
                        $guid = $firstItem['GUID'];
                    }
                } elseif (is_array($responseData) && isset($responseData['GUID'])) {
                    // إذا كانت الاستجابة object مباشر
                    $guid = $responseData['GUID'];
                }

                if ($guid && $guid !== '00000000-0000-0000-0000-000000000000') {
                    $order->update([
                        'external_guid' => $guid,
                        'external_sent_at' => now()
                    ]);

                    Log::info("Order {$order->id} sent successfully with GUID: {$guid}");
                    return true;
                } else {
                    Log::warning("Order {$order->id} sent but no valid GUID returned", $responseData);
                    $order->update(['external_sent_at' => now()]);
                    return true;
                }
            } else {
                Log::error("Failed to send order {$order->id} to external API", [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error("Exception while sending order {$order->id} to external API: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إعداد بيانات الفاتورة للإرسال
     */
    private function prepareInvoiceData(Order $order)
    {
        // حساب المجاميع
        $subtotal = $order->order_amount - $order->delivery_charge;
        $vat = 0; // يمكن حسابها حسب النظام
        $totalDiscount = $order->coupon_discount_amount ?? 0;
        $totalExtra = $order->delivery_charge ?? 0;

        // تحديد نوع الدفع
        $payType = $order->payment_method === 'cash_on_delivery' ? 0 : 1;

        // إعداد تفاصيل المواد
        $details = [];
        $orderDetails = $order->details ?? [];

        foreach ($orderDetails as $index => $detail) {
            $details[] = [
                "Qty" => $detail->quantity ?? 1,
                "Price" => $detail->price ?? 0,
                "MatGUID" => $this->getProductGUID($detail->food_id ?? 0),
                "StoreGUID" => $this->defaultStoreGUID,
                "CostGUID" => $this->defaultCostGUID,
                "VAT" => 0,
                "Discount" => 0,
                "Extra" => 0
            ];
        }

        // إذا لم توجد تفاصيل، أضف عنصر افتراضي
        if (empty($details)) {
            $details[] = [
                "Qty" => 1,
                "Price" => $subtotal,
                "MatGUID" => $this->getDefaultProductGUID(),
                "StoreGUID" => $this->defaultStoreGUID,
                "CostGUID" => $this->defaultCostGUID,
                "VAT" => 0,
                "Discount" => 0,
                "Extra" => 0
            ];
        }

        return [
            "Total" => $subtotal,
            "VAT" => $vat,
            "TotalDisc" => $totalDiscount,
            "TotalExtra" => $totalExtra,
            "PayType" => $payType,
            "Notes" => "Order #{$order->id} - {$order->customer->f_name} {$order->customer->l_name}",
            "TypeGUID" => $this->defaultTypeGUID,
            "CustGUID" => $this->getCustomerGUID($order->user_id),
            "StoreGUID" => $this->defaultStoreGUID,
            "CustAccGUID" => $this->defaultCustAccGUID,
            "CostGUID" => $this->defaultCostGUID,
            "Details" => $details
        ];
    }

    /**
     * الحصول على GUID المنتج
     */
    private function getProductGUID($productId)
    {
        // يمكن إضافة جدول mapping للمنتجات
        // أو استخدام GUID افتراضي من الاستجابة
        return '9D170AC7-EFAC-48BD-BA21-20F651E31D2A'; // GUID من الاستجابة
    }

    /**
     * الحصول على GUID المنتج الافتراضي
     */
    private function getDefaultProductGUID()
    {
        return '9D170AC7-EFAC-48BD-BA21-20F651E31D2A'; // GUID من الاستجابة
    }

    /**
     * الحصول على GUID العميل
     */
    private function getCustomerGUID($customerId)
    {
        // يمكن إضافة جدول mapping للعملاء
        // أو استخدام GUID افتراضي للعملاء النقديين
        return $this->defaultCustGUID;
    }
}
