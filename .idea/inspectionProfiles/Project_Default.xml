<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="CssUnknownProperty" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myCustomPropertiesEnabled" value="true" />
      <option name="myIgnoreVendorSpecificProperties" value="false" />
      <option name="myCustomPropertiesList">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="enable-background" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="HtmlUnknownAttribute" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="height" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
    <inspection_tool class="HtmlUnknownTag" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="myValues">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="nobr" />
            <item index="1" class="java.lang.String" itemvalue="noembed" />
            <item index="2" class="java.lang.String" itemvalue="comment" />
            <item index="3" class="java.lang.String" itemvalue="noscript" />
            <item index="4" class="java.lang.String" itemvalue="embed" />
            <item index="5" class="java.lang.String" itemvalue="script" />
            <item index="6" class="java.lang.String" itemvalue="div" />
            <item index="7" class="java.lang.String" itemvalue="form" />
            <item index="8" class="java.lang.String" itemvalue="tbody" />
            <item index="9" class="java.lang.String" itemvalue="span" />
            <item index="10" class="java.lang.String" itemvalue="br" />
          </list>
        </value>
      </option>
      <option name="myCustomValuesEnabled" value="true" />
    </inspection_tool>
  </profile>
</component>