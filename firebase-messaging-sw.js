importScripts('https://www.gstatic.com/firebasejs/8.3.2/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.3.2/firebase-messaging.js');

firebase.initializeApp({
    apiKey: "AIzaSyCuDEpgaRHwfL9DgR53SnT4c1Hr03jmrnA",
    authDomain: "twin-market-app.firebaseapp.com",
    projectId: "twin-market-app",
    storageBucket: "twin-market-app.firebasestorage.app",
    messagingSenderId: "457377941870",
    appId: "1:457377941870:web:d3c40c6cee92e4f443997f",
    measurementId: "G-FZ7PM9Q7P2"
});

const messaging = firebase.messaging();
messaging.setBackgroundMessageHandler(function (payload) {
    return self.registration.showNotification(payload.data.title, {
        body: payload.data.body ? payload.data.body : '',
        icon: payload.data.icon ? payload.data.icon : ''
    });
});