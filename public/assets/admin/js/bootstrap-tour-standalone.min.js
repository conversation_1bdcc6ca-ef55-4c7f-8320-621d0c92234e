/* ========================================================================
 * bootstrap-tour - v0.12.0
 * http://bootstraptour.com
 * ========================================================================
 * Copyright 2012-2017 Ulrich Sossou
 *
 * ========================================================================
 * Licensed under the MIT License (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://opensource.org/licenses/MIT
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ========================================================================
 */

function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _possibleConstructorReturn(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Popper=e()}(this,function(){"use strict";function t(t){var e={};return t&&"[object Function]"===e.toString.call(t)}function e(t,e){if(1!==t.nodeType)return[];var n=window.getComputedStyle(t,null);return e?n[e]:n}function n(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function o(t){if(!t||-1!==["HTML","BODY","#document"].indexOf(t.nodeName))return window.document.body;var i=e(t),r=i.overflow,s=i.overflowX,a=i.overflowY;return/(auto|scroll)/.test(r+a+s)?t:o(n(t))}function i(t){var n=t&&t.offsetParent,o=n&&n.nodeName;return o&&"BODY"!==o&&"HTML"!==o?-1!==["TD","TABLE"].indexOf(n.nodeName)&&"static"===e(n,"position")?i(n):n:window.document.documentElement}function r(t){var e=t.nodeName;return"BODY"!==e&&("HTML"===e||i(t.firstElementChild)===t)}function s(t){return null!==t.parentNode?s(t.parentNode):t}function a(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return window.document.documentElement;var n=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,o=n?t:e,p=n?e:t,u=document.createRange();u.setStart(o,0),u.setEnd(p,0);var l=u.commonAncestorContainer;if(t!==l&&e!==l||o.contains(p))return r(l)?l:i(l);var c=s(t);return c.host?a(c.host,e):a(t,s(e).host)}function p(t){var e="top"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top")?"scrollTop":"scrollLeft",n=t.nodeName;if("BODY"===n||"HTML"===n){var o=window.document.documentElement;return(window.document.scrollingElement||o)[e]}return t[e]}function u(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=p(e,"top"),i=p(e,"left"),r=n?-1:1;return t.top+=o*r,t.bottom+=o*r,t.left+=i*r,t.right+=i*r,t}function l(t,e){var n="x"===e?"Left":"Top",o="Left"===n?"Right":"Bottom";return+t["border"+n+"Width"].split("px")[0]+ +t["border"+o+"Width"].split("px")[0]}function c(t,e,n,o){return Math.max(e["offset"+t],e["scroll"+t],n["client"+t],n["offset"+t],n["scroll"+t],Z()?n["offset"+t]+o["margin"+("Height"===t?"Top":"Left")]+o["margin"+("Height"===t?"Bottom":"Right")]:0)}function h(){var t=window.document.body,e=window.document.documentElement,n=Z()&&window.getComputedStyle(e);return{height:c("Height",t,e,n),width:c("Width",t,e,n)}}function f(t){return nt({},t,{right:t.left+t.width,bottom:t.top+t.height})}function d(t){var n={};if(Z())try{n=t.getBoundingClientRect();var o=p(t,"top"),i=p(t,"left");n.top+=o,n.left+=i,n.bottom+=o,n.right+=i}catch(t){}else n=t.getBoundingClientRect();var r={left:n.left,top:n.top,width:n.right-n.left,height:n.bottom-n.top},s="HTML"===t.nodeName?h():{},a=s.width||t.clientWidth||r.right-r.left,u=s.height||t.clientHeight||r.bottom-r.top,c=t.offsetWidth-a,d=t.offsetHeight-u;if(c||d){var m=e(t);c-=l(m,"x"),d-=l(m,"y"),r.width-=c,r.height-=d}return f(r)}function m(t,n){var i=Z(),r="HTML"===n.nodeName,s=d(t),a=d(n),p=o(t),l=e(n),c=+l.borderTopWidth.split("px")[0],h=+l.borderLeftWidth.split("px")[0],m=f({top:s.top-a.top-c,left:s.left-a.left-h,width:s.width,height:s.height});if(m.marginTop=0,m.marginLeft=0,!i&&r){var g=+l.marginTop.split("px")[0],v=+l.marginLeft.split("px")[0];m.top-=c-g,m.bottom-=c-g,m.left-=h-v,m.right-=h-v,m.marginTop=g,m.marginLeft=v}return(i?n.contains(p):n===p&&"BODY"!==p.nodeName)&&(m=u(m,n)),m}function g(t){var e=window.document.documentElement,n=m(t,e),o=Math.max(e.clientWidth,window.innerWidth||0),i=Math.max(e.clientHeight,window.innerHeight||0),r=p(e),s=p(e,"left");return f({top:r-n.top+n.marginTop,left:s-n.left+n.marginLeft,width:o,height:i})}function v(t){var o=t.nodeName;return"BODY"!==o&&"HTML"!==o&&("fixed"===e(t,"position")||v(n(t)))}function _(t,e,i,r){var s={top:0,left:0},p=a(t,e);if("viewport"===r)s=g(p);else{var u=void 0;"scrollParent"===r?"BODY"===(u=o(n(t))).nodeName&&(u=window.document.documentElement):u="window"===r?window.document.documentElement:r;var l=m(u,p);if("HTML"!==u.nodeName||v(p))s=l;else{var c=h(),f=c.height,d=c.width;s.top+=l.top-l.marginTop,s.bottom=f+l.top,s.left+=l.left-l.marginLeft,s.right=d+l.left}}return s.left+=i,s.top+=i,s.right-=i,s.bottom-=i,s}function b(t){return t.width*t.height}function y(t,e,n,o,i){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var s=_(n,o,r,i),a={top:{width:s.width,height:e.top-s.top},right:{width:s.right-e.right,height:s.height},bottom:{width:s.width,height:s.bottom-e.bottom},left:{width:e.left-s.left,height:s.height}},p=Object.keys(a).map(function(t){return nt({key:t},a[t],{area:b(a[t])})}).sort(function(t,e){return e.area-t.area}),u=p.filter(function(t){var e=t.width,o=t.height;return e>=n.clientWidth&&o>=n.clientHeight}),l=u.length>0?u[0].key:p[0].key,c=t.split("-")[1];return l+(c?"-"+c:"")}function w(t,e,n){return m(n,a(e,n))}function E(t){var e=window.getComputedStyle(t),n=parseFloat(e.marginTop)+parseFloat(e.marginBottom),o=parseFloat(e.marginLeft)+parseFloat(e.marginRight);return{width:t.offsetWidth+o,height:t.offsetHeight+n}}function S(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,function(t){return e[t]})}function T(t,e,n){n=n.split("-")[0];var o=E(t),i={width:o.width,height:o.height},r=-1!==["right","left"].indexOf(n),s=r?"top":"left",a=r?"left":"top",p=r?"height":"width",u=r?"width":"height";return i[s]=e[s]+e[p]/2-o[p]/2,i[a]=n===a?e[a]-o[u]:e[S(a)],i}function O(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function C(t,e,n){if(Array.prototype.findIndex)return t.findIndex(function(t){return t[e]===n});var o=O(t,function(t){return t[e]===n});return t.indexOf(o)}function x(e,n,o){return(void 0===o?e:e.slice(0,C(e,"name",o))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var o=e.function||e.fn;e.enabled&&t(o)&&(n.offsets.popper=f(n.offsets.popper),n.offsets.reference=f(n.offsets.reference),n=o(n,e))}),n}function k(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=w(this.state,this.popper,this.reference),t.placement=y(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.offsets.popper=T(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position="absolute",t=x(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}function P(t,e){return t.some(function(t){var n=t.name;return t.enabled&&n===e})}function D(t){for(var e=[!1,"ms","Webkit","Moz","O"],n=t.charAt(0).toUpperCase()+t.slice(1),o=0;o<e.length-1;o++){var i=e[o],r=i?""+i+n:t;if(void 0!==window.document.body.style[r])return r}return null}function N(){return this.state.isDestroyed=!0,P(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.left="",this.popper.style.position="",this.popper.style.top="",this.popper.style[D("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function A(t,e,n,i){var r="BODY"===t.nodeName,s=r?window:t;s.addEventListener(e,n,{passive:!0}),r||A(o(s.parentNode),e,n,i),i.push(s)}function H(t,e,n,i){n.updateBound=i,window.addEventListener("resize",n.updateBound,{passive:!0});var r=o(t);return A(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function j(){this.state.eventsEnabled||(this.state=H(this.reference,this.options,this.state,this.scheduleUpdate))}function I(t,e){return window.removeEventListener("resize",e.updateBound),e.scrollParents.forEach(function(t){t.removeEventListener("scroll",e.updateBound)}),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e}function R(){this.state.eventsEnabled&&(window.cancelAnimationFrame(this.scheduleUpdate),this.state=I(this.reference,this.state))}function L(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function W(t,e){Object.keys(e).forEach(function(n){var o="";-1!==["width","height","top","right","bottom","left"].indexOf(n)&&L(e[n])&&(o="px"),t.style[n]=e[n]+o})}function U(t,e){Object.keys(e).forEach(function(n){!1!==e[n]?t.setAttribute(n,e[n]):t.removeAttribute(n)})}function M(t,e,n){var o=O(t,function(t){return t.name===e}),i=!!o&&t.some(function(t){return t.name===n&&t.enabled&&t.order<o.order});if(!i){var r="`"+e+"`",s="`"+n+"`";console.warn(s+" modifier is required by "+r+" modifier in order to work, be sure to include it before "+r+"!")}return i}function F(t){return"end"===t?"start":"start"===t?"end":t}function B(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=it.indexOf(t),o=it.slice(n+1).concat(it.slice(0,n));return e?o.reverse():o}function K(t,e,n,o){var i=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+i[1],s=i[2];if(!r)return t;if(0===s.indexOf("%")){var a=void 0;switch(s){case"%p":a=n;break;case"%":case"%r":default:a=o}return f(a)[e]/100*r}if("vh"===s||"vw"===s){return("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*r}return r}function q(t,e,n,o){var i=[0,0],r=-1!==["right","left"].indexOf(o),s=t.split(/(\+|\-)/).map(function(t){return t.trim()}),a=s.indexOf(O(s,function(t){return-1!==t.search(/,|\s/)}));s[a]&&-1===s[a].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var p=/\s*,\s*|\s+/,u=-1!==a?[s.slice(0,a).concat([s[a].split(p)[0]]),[s[a].split(p)[1]].concat(s.slice(a+1))]:[s];return(u=u.map(function(t,o){var i=(1===o?!r:r)?"height":"width",s=!1;return t.reduce(function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,s=!0,t):s?(t[t.length-1]+=e,s=!1,t):t.concat(e)},[]).map(function(t){return K(t,i,e,n)})})).forEach(function(t,e){t.forEach(function(n,o){L(n)&&(i[e]+=n*("-"===t[o-1]?-1:1))})}),i}for(var Y=["native code","[object MutationObserverConstructor]"],V="undefined"!=typeof window,Q=["Edge","Trident","Firefox"],G=0,z=0;z<Q.length;z+=1)if(V&&navigator.userAgent.indexOf(Q[z])>=0){G=1;break}var J=V&&function(t){return Y.some(function(e){return(t||"").toString().indexOf(e)>-1})}(window.MutationObserver)?function(t){var e=!1,n=0,o=document.createElement("span");return new MutationObserver(function(){t(),e=!1}).observe(o,{attributes:!0}),function(){e||(e=!0,o.setAttribute("x-index",n),n+=1)}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout(function(){e=!1,t()},G))}},X=void 0,Z=function(){return void 0===X&&(X=-1!==navigator.appVersion.indexOf("MSIE 10")),X},$=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},tt=function(){function t(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}}(),et=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},nt=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},ot=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],it=ot.slice(3),rt={FLIP:"flip",CLOCKWISE:"clockwise",COUNTERCLOCKWISE:"counterclockwise"},st={placement:"bottom",eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,n=e.split("-")[0],o=e.split("-")[1];if(o){var i=t.offsets,r=i.reference,s=i.popper,a=-1!==["bottom","top"].indexOf(n),p=a?"left":"top",u=a?"width":"height",l={start:et({},p,r[p]),end:et({},p,r[p]+r[u]-s[u])};t.offsets.popper=nt({},s,l[o])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var n=e.offset,o=t.placement,i=t.offsets,r=i.popper,s=i.reference,a=o.split("-")[0],p=void 0;return p=L(+n)?[+n,0]:q(n,r,s,a),"left"===a?(r.top+=p[0],r.left-=p[1]):"right"===a?(r.top+=p[0],r.left+=p[1]):"top"===a?(r.left+=p[0],r.top-=p[1]):"bottom"===a&&(r.left+=p[0],r.top+=p[1]),t.popper=r,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var n=e.boundariesElement||i(t.instance.popper);t.instance.reference===n&&(n=i(n));var o=_(t.instance.popper,t.instance.reference,e.padding,n);e.boundaries=o;var r=e.priority,s=t.offsets.popper,a={primary:function(t){var n=s[t];return s[t]<o[t]&&!e.escapeWithReference&&(n=Math.max(s[t],o[t])),et({},t,n)},secondary:function(t){var n="right"===t?"left":"top",i=s[n];return s[t]>o[t]&&!e.escapeWithReference&&(i=Math.min(s[n],o[t]-("right"===t?s.width:s.height))),et({},n,i)}};return r.forEach(function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";s=nt({},s,a[e](t))}),t.offsets.popper=s,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,n=e.popper,o=e.reference,i=t.placement.split("-")[0],r=Math.floor,s=-1!==["top","bottom"].indexOf(i),a=s?"right":"bottom",p=s?"left":"top",u=s?"width":"height";return n[a]<r(o[p])&&(t.offsets.popper[p]=r(o[p])-n[u]),n[p]>r(o[a])&&(t.offsets.popper[p]=r(o[a])),t}},arrow:{order:500,enabled:!0,fn:function(t,n){if(!M(t.instance.modifiers,"arrow","keepTogether"))return t;var o=n.element;if("string"==typeof o){if(!(o=t.instance.popper.querySelector(o)))return t}else if(!t.instance.popper.contains(o))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var i=t.placement.split("-")[0],r=t.offsets,s=r.popper,a=r.reference,p=-1!==["left","right"].indexOf(i),u=p?"height":"width",l=p?"Top":"Left",c=l.toLowerCase(),h=p?"left":"top",d=p?"bottom":"right",m=E(o)[u];a[d]-m<s[c]&&(t.offsets.popper[c]-=s[c]-(a[d]-m)),a[c]+m>s[d]&&(t.offsets.popper[c]+=a[c]+m-s[d]);var g=a[c]+a[u]/2-m/2,v=e(t.instance.popper,"margin"+l).replace("px",""),_=g-f(t.offsets.popper)[c]-v;return _=Math.max(Math.min(s[u]-m,_),0),t.arrowElement=o,t.offsets.arrow={},t.offsets.arrow[c]=Math.round(_),t.offsets.arrow[h]="",t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(P(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var n=_(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement),o=t.placement.split("-")[0],i=S(o),r=t.placement.split("-")[1]||"",s=[];switch(e.behavior){case rt.FLIP:s=[o,i];break;case rt.CLOCKWISE:s=B(o);break;case rt.COUNTERCLOCKWISE:s=B(o,!0);break;default:s=e.behavior}return s.forEach(function(a,p){if(o!==a||s.length===p+1)return t;o=t.placement.split("-")[0],i=S(o);var u=t.offsets.popper,l=t.offsets.reference,c=Math.floor,h="left"===o&&c(u.right)>c(l.left)||"right"===o&&c(u.left)<c(l.right)||"top"===o&&c(u.bottom)>c(l.top)||"bottom"===o&&c(u.top)<c(l.bottom),f=c(u.left)<c(n.left),d=c(u.right)>c(n.right),m=c(u.top)<c(n.top),g=c(u.bottom)>c(n.bottom),v="left"===o&&f||"right"===o&&d||"top"===o&&m||"bottom"===o&&g,_=-1!==["top","bottom"].indexOf(o),b=!!e.flipVariations&&(_&&"start"===r&&f||_&&"end"===r&&d||!_&&"start"===r&&m||!_&&"end"===r&&g);(h||v||b)&&(t.flipped=!0,(h||v)&&(o=s[p+1]),b&&(r=F(r)),t.placement=o+(r?"-"+r:""),t.offsets.popper=nt({},t.offsets.popper,T(t.instance.popper,t.offsets.reference,t.placement)),t=x(t.instance.modifiers,t,"flip"))}),t},behavior:"flip",padding:5,boundariesElement:"viewport"},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,n=e.split("-")[0],o=t.offsets,i=o.popper,r=o.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return i[s?"left":"top"]=r[n]-(a?i[s?"width":"height"]:0),t.placement=S(e),t.offsets.popper=f(i),t}},hide:{order:800,enabled:!0,fn:function(t){if(!M(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,n=O(t.instance.modifiers,function(t){return"preventOverflow"===t.name}).boundaries;if(e.bottom<n.top||e.left>n.right||e.top>n.bottom||e.right<n.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var n=e.x,o=e.y,r=t.offsets.popper,s=O(t.instance.modifiers,function(t){return"applyStyle"===t.name}).gpuAcceleration;void 0!==s&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a=void 0!==s?s:e.gpuAcceleration,p=d(i(t.instance.popper)),u={position:r.position},l={left:Math.floor(r.left),top:Math.floor(r.top),bottom:Math.floor(r.bottom),right:Math.floor(r.right)},c="bottom"===n?"top":"bottom",h="right"===o?"left":"right",f=D("transform"),m=void 0,g=void 0;if(g="bottom"===c?-p.height+l.bottom:l.top,m="right"===h?-p.width+l.right:l.left,a&&f)u[f]="translate3d("+m+"px, "+g+"px, 0)",u[c]=0,u[h]=0,u.willChange="transform";else{var v="bottom"===c?-1:1,_="right"===h?-1:1;u[c]=g*v,u[h]=m*_,u.willChange=c+", "+h}var b={"x-placement":t.placement};return t.attributes=nt({},b,t.attributes),t.styles=nt({},u,t.styles),t.arrowStyles=nt({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){return W(t.instance.popper,t.styles),U(t.instance.popper,t.attributes),t.arrowElement&&Object.keys(t.arrowStyles).length&&W(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,n,o,i){var r=w(i,e,t),s=y(n.placement,r,e,t,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return e.setAttribute("x-placement",s),W(e,{position:"absolute"}),n},gpuAcceleration:void 0}}},at=function(){function e(n,o){var i=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};$(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(i.update)},this.update=J(this.update.bind(this)),this.options=nt({},e.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=n.jquery?n[0]:n,this.popper=o.jquery?o[0]:o,this.options.modifiers={},Object.keys(nt({},e.Defaults.modifiers,r.modifiers)).forEach(function(t){i.options.modifiers[t]=nt({},e.Defaults.modifiers[t]||{},r.modifiers?r.modifiers[t]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(t){return nt({name:t},i.options.modifiers[t])}).sort(function(t,e){return t.order-e.order}),this.modifiers.forEach(function(e){e.enabled&&t(e.onLoad)&&e.onLoad(i.reference,i.popper,i.options,e,i.state)}),this.update();var s=this.options.eventsEnabled;s&&this.enableEventListeners(),this.state.eventsEnabled=s}return tt(e,[{key:"update",value:function(){return k.call(this)}},{key:"destroy",value:function(){return N.call(this)}},{key:"enableEventListeners",value:function(){return j.call(this)}},{key:"disableEventListeners",value:function(){return R.call(this)}}]),e}();return at.Utils=("undefined"!=typeof window?window:global).PopperUtils,at.placements=ot,at.Defaults=st,at});var Util=function(t){function e(t){return{}.toString.call(t).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function n(t){return(t[0]||t).nodeType}function o(){return{bindType:s.end,delegateType:s.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}}}function i(){if(window.QUnit)return!1;var t=document.createElement("bootstrap");for(var e in a)if(void 0!==t.style[e])return{end:a[e]};return!1}function r(e){var n=this,o=!1;return t(this).one(p.TRANSITION_END,function(){o=!0}),setTimeout(function(){o||p.triggerTransitionEnd(n)},e),this}var s=!1,a={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"},p={TRANSITION_END:"bsTransitionEnd",getUID:function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},getSelectorFromElement:function(e){var n=e.getAttribute("data-target");n&&"#"!==n||(n=e.getAttribute("href")||"");try{return t(n).length>0?n:null}catch(t){return null}},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(e){t(e).trigger(s.end)},supportsTransitionEnd:function(){return Boolean(s)},typeCheckConfig:function(t,o,i){for(var r in i)if(i.hasOwnProperty(r)){var s=i[r],a=o[r],p=a&&n(a)?"element":e(a);if(!new RegExp(s).test(p))throw new Error(t.toUpperCase()+': Option "'+r+'" provided type "'+p+'" but expected type "'+s+'".')}}};return s=i(),t.fn.emulateTransitionEnd=r,p.supportsTransitionEnd()&&(t.event.special[p.TRANSITION_END]=o()),p}(jQuery),_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_createClass=function(){function t(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}}(),Tooltip=function(t){if("undefined"==typeof Popper)throw new Error("Bootstrap tooltips require Popper.js (https://popper.js.org)");var e="tooltip",n=".bs.tooltip",o=t.fn[e],i=new RegExp("(^|\\s)bs-tooltip\\S+","g"),r={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)"},s={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},a={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip"},p={SHOW:"show",OUT:"out"},u={HIDE:"hide"+n,HIDDEN:"hidden"+n,SHOW:"show"+n,SHOWN:"shown"+n,INSERTED:"inserted"+n,CLICK:"click"+n,FOCUSIN:"focusin"+n,FOCUSOUT:"focusout"+n,MOUSEENTER:"mouseenter"+n,MOUSELEAVE:"mouseleave"+n},l={FADE:"fade",SHOW:"show"},c={TOOLTIP:".tooltip",TOOLTIP_INNER:".tooltip-inner",ARROW:".arrow"},h={HOVER:"hover",FOCUS:"focus",CLICK:"click",MANUAL:"manual"},f=function(){function o(t,e){_classCallCheck(this,o),this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}return o.prototype.enable=function(){this._isEnabled=!0},o.prototype.disable=function(){this._isEnabled=!1},o.prototype.toggleEnabled=function(){this._isEnabled=!this._isEnabled},o.prototype.toggle=function(e){if(e){var n=this.constructor.DATA_KEY,o=t(e.currentTarget).data(n);o||(o=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(n,o)),o._activeTrigger.click=!o._activeTrigger.click,o._isWithActiveTrigger()?o._enter(null,o):o._leave(null,o)}else{if(t(this.getTipElement()).hasClass(l.SHOW))return void this._leave(null,this);this._enter(null,this)}},o.prototype.dispose=function(){clearTimeout(this._timeout),t.removeData(this.element,this.constructor.DATA_KEY),t(this.element).off(this.constructor.EVENT_KEY),t(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&t(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,null!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},o.prototype.show=function(){var e=this;if("none"===t(this.element).css("display"))throw new Error("Please use show on visible elements");var n=t.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){t(this.element).trigger(n);var i=t.contains(this.element.ownerDocument.documentElement,this.element);if(n.isDefaultPrevented()||!i)return;var r=this.getTipElement(),s=Util.getUID(this.constructor.NAME);r.setAttribute("id",s),this.element.setAttribute("aria-describedby",s),this.setContent(),this.config.animation&&t(r).addClass(l.FADE);var a="function"==typeof this.config.placement?this.config.placement.call(this,r,this.element):this.config.placement,u=this._getAttachment(a);this.addAttachmentClass(u);var h=!1===this.config.container?document.body:t(this.config.container);t(r).data(this.constructor.DATA_KEY,this),t.contains(this.element.ownerDocument.documentElement,this.tip)||t(r).appendTo(h),t(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new Popper(this.element,r,{placement:u,modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:c.ARROW}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){e._handlePopperPlacementChange(t)}}),t(r).addClass(l.SHOW),"ontouchstart"in document.documentElement&&t("body").children().on("mouseover",null,t.noop);var f=function(){e.config.animation&&e._fixTransition();var n=e._hoverState;e._hoverState=null,t(e.element).trigger(e.constructor.Event.SHOWN),n===p.OUT&&e._leave(null,e)};Util.supportsTransitionEnd()&&t(this.tip).hasClass(l.FADE)?t(this.tip).one(Util.TRANSITION_END,f).emulateTransitionEnd(o._TRANSITION_DURATION):f()}},o.prototype.hide=function(e){var n=this,o=this.getTipElement(),i=t.Event(this.constructor.Event.HIDE),r=function(){n._hoverState!==p.SHOW&&o.parentNode&&o.parentNode.removeChild(o),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),t(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()};t(this.element).trigger(i),i.isDefaultPrevented()||(t(o).removeClass(l.SHOW),"ontouchstart"in document.documentElement&&t("body").children().off("mouseover",null,t.noop),this._activeTrigger[h.CLICK]=!1,this._activeTrigger[h.FOCUS]=!1,this._activeTrigger[h.HOVER]=!1,Util.supportsTransitionEnd()&&t(this.tip).hasClass(l.FADE)?t(o).one(Util.TRANSITION_END,r).emulateTransitionEnd(150):r(),this._hoverState="")},o.prototype.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},o.prototype.isWithContent=function(){return Boolean(this.getTitle())},o.prototype.addAttachmentClass=function(e){t(this.getTipElement()).addClass("bs-tooltip-"+e)},o.prototype.getTipElement=function(){return this.tip=this.tip||t(this.config.template)[0]},o.prototype.setContent=function(){var e=t(this.getTipElement());this.setElementContent(e.find(c.TOOLTIP_INNER),this.getTitle()),e.removeClass(l.FADE+" "+l.SHOW)},o.prototype.setElementContent=function(e,n){var o=this.config.html;"object"===(void 0===n?"undefined":_typeof(n))&&(n.nodeType||n.jquery)?o?t(n).parent().is(e)||e.empty().append(n):e.text(t(n).text()):e[o?"html":"text"](n)},o.prototype.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||(t="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),t},o.prototype._getAttachment=function(t){return s[t.toUpperCase()]},o.prototype._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach(function(n){if("click"===n)t(e.element).on(e.constructor.Event.CLICK,e.config.selector,function(t){return e.toggle(t)});else if(n!==h.MANUAL){var o=n===h.HOVER?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,i=n===h.HOVER?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;t(e.element).on(o,e.config.selector,function(t){return e._enter(t)}).on(i,e.config.selector,function(t){return e._leave(t)})}t(e.element).closest(".modal").on("hide.bs.modal",function(){return e.hide()})}),this.config.selector?this.config=t.extend({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},o.prototype._fixTitle=function(){var t=_typeof(this.element.getAttribute("data-original-title"));(this.element.getAttribute("title")||"string"!==t)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},o.prototype._enter=function(e,n){var o=this.constructor.DATA_KEY;(n=n||t(e.currentTarget).data(o))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(o,n)),e&&(n._activeTrigger["focusin"===e.type?h.FOCUS:h.HOVER]=!0),t(n.getTipElement()).hasClass(l.SHOW)||n._hoverState===p.SHOW?n._hoverState=p.SHOW:(clearTimeout(n._timeout),n._hoverState=p.SHOW,n.config.delay&&n.config.delay.show?n._timeout=setTimeout(function(){n._hoverState===p.SHOW&&n.show()},n.config.delay.show):n.show())},o.prototype._leave=function(e,n){var o=this.constructor.DATA_KEY;(n=n||t(e.currentTarget).data(o))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(o,n)),e&&(n._activeTrigger["focusout"===e.type?h.FOCUS:h.HOVER]=!1),n._isWithActiveTrigger()||(clearTimeout(n._timeout),n._hoverState=p.OUT,n.config.delay&&n.config.delay.hide?n._timeout=setTimeout(function(){n._hoverState===p.OUT&&n.hide()},n.config.delay.hide):n.hide())},o.prototype._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},o.prototype._getConfig=function(n){return(n=t.extend({},this.constructor.Default,t(this.element).data(),n)).delay&&"number"==typeof n.delay&&(n.delay={show:n.delay,hide:n.delay}),n.title&&"number"==typeof n.title&&(n.title=n.title.toString()),n.content&&"number"==typeof n.content&&(n.content=n.content.toString()),Util.typeCheckConfig(e,n,this.constructor.DefaultType),n},o.prototype._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},o.prototype._cleanTipClass=function(){var e=t(this.getTipElement()),n=e.attr("class").match(i);null!==n&&n.length>0&&e.removeClass(n.join(""))},o.prototype._handlePopperPlacementChange=function(t){this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},o.prototype._fixTransition=function(){var e=this.getTipElement(),n=this.config.animation;null===e.getAttribute("x-placement")&&(t(e).removeClass(l.FADE),this.config.animation=!1,this.hide(),this.show(),this.config.animation=n)},o._jQueryInterface=function(e){return this.each(function(){var n=t(this).data("bs.tooltip"),i="object"===(void 0===e?"undefined":_typeof(e))&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new o(this,i),t(this).data("bs.tooltip",n)),"string"==typeof e)){if(void 0===n[e])throw new Error('No method named "'+e+'"');n[e]()}})},_createClass(o,null,[{key:"VERSION",get:function(){return"4.0.0-beta"}},{key:"Default",get:function(){return a}},{key:"NAME",get:function(){return e}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return u}},{key:"EVENT_KEY",get:function(){return n}},{key:"DefaultType",get:function(){return r}}]),o}();return t.fn[e]=f._jQueryInterface,t.fn[e].Constructor=f,t.fn[e].noConflict=function(){return t.fn[e]=o,f._jQueryInterface},f}(jQuery),_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_createClass=function(){function t(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}}(),Popover=function(t){var e="popover",n=".bs.popover",o=t.fn[e],i=new RegExp("(^|\\s)bs-popover\\S+","g"),r=t.extend({},Tooltip.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),s=t.extend({},Tooltip.DefaultType,{content:"(string|element|function)"}),a={FADE:"fade",SHOW:"show"},p={TITLE:".popover-header",CONTENT:".popover-body"},u={HIDE:"hide"+n,HIDDEN:"hidden"+n,SHOW:"show"+n,SHOWN:"shown"+n,INSERTED:"inserted"+n,CLICK:"click"+n,FOCUSIN:"focusin"+n,FOCUSOUT:"focusout"+n,MOUSEENTER:"mouseenter"+n,MOUSELEAVE:"mouseleave"+n},l=function(o){function l(){return _classCallCheck(this,l),_possibleConstructorReturn(this,o.apply(this,arguments))}return _inherits(l,o),l.prototype.isWithContent=function(){return this.getTitle()||this._getContent()},l.prototype.addAttachmentClass=function(e){t(this.getTipElement()).addClass("bs-popover-"+e)},l.prototype.getTipElement=function(){return this.tip=this.tip||t(this.config.template)[0]},l.prototype.setContent=function(){var e=t(this.getTipElement());this.setElementContent(e.find(p.TITLE),this.getTitle()),this.setElementContent(e.find(p.CONTENT),this._getContent()),e.removeClass(a.FADE+" "+a.SHOW)},l.prototype._getContent=function(){return this.element.getAttribute("data-content")||("function"==typeof this.config.content?this.config.content.call(this.element):this.config.content)},l.prototype._cleanTipClass=function(){var e=t(this.getTipElement()),n=e.attr("class").match(i);null!==n&&n.length>0&&e.removeClass(n.join(""))},l._jQueryInterface=function(e){return this.each(function(){var n=t(this).data("bs.popover"),o="object"===(void 0===e?"undefined":_typeof(e))?e:null;if((n||!/destroy|hide/.test(e))&&(n||(n=new l(this,o),t(this).data("bs.popover",n)),"string"==typeof e)){if(void 0===n[e])throw new Error('No method named "'+e+'"');n[e]()}})},_createClass(l,null,[{key:"VERSION",get:function(){return"4.0.0-beta"}},{key:"Default",get:function(){return r}},{key:"NAME",get:function(){return e}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return u}},{key:"EVENT_KEY",get:function(){return n}},{key:"DefaultType",get:function(){return s}}]),l}(Tooltip);return t.fn[e]=l._jQueryInterface,t.fn[e].Constructor=l,t.fn[e].noConflict=function(){return t.fn[e]=o,l._jQueryInterface},l}(jQuery),bind=function(t,e){return function(){return t.apply(e,arguments)}};!function(t,e){"function"==typeof define&&define.amd?define(["jquery"],function(n){return t.Tour=e(n)}):"object"==typeof exports?module.exports=e(require("jquery")):t.Tour=e(t.jQuery)}(window,function(t){var e;return e=window.document,function(){function n(e){this._showPopoverAndOverlay=bind(this._showPopoverAndOverlay,this);var n;try{n=window.localStorage}catch(t){n=!1}this._options=t.extend({name:"tour",steps:[],container:"body",autoscroll:!0,keyboard:!0,storage:n,debug:!1,backdrop:!1,backdropContainer:"body",backdropPadding:0,redirect:!0,orphan:!1,duration:!1,delay:!1,basePath:"",template:'<div class="popover" role="tooltip"> <div class="arrow"></div> <h3 class="popover-header"></h3> <div class="popover-body"></div> <div class="popover-navigation"> <div class="btn-group"> <button class="btn btn-sm btn-secondary" data-role="prev">&laquo; Prev</button> <button class="btn btn-sm btn-secondary" data-role="next">Next &raquo;</button> <button class="btn btn-sm btn-secondary" data-role="pause-resume" data-pause-text="Pause" data-resume-text="Resume">Pause</button> </div> <button class="btn btn-sm btn-secondary" data-role="end">End tour</button> </div> </div>',afterSetState:function(t,e){},afterGetState:function(t,e){},afterRemoveState:function(t){},onStart:function(t){},onEnd:function(t){},onShow:function(t){},onShown:function(t){},onHide:function(t){},onHidden:function(t){},onNext:function(t){},onPrev:function(t){},onPause:function(t,e){},onResume:function(t,e){},onRedirectError:function(t){}},e),this._force=!1,this._inited=!1,this._current=null,this.backdrops=[]}return n.prototype.addSteps=function(t){var e,n,o;for(e=0,n=t.length;e<n;e++)o=t[e],this.addStep(o);return this},n.prototype.addStep=function(t){return this._options.steps.push(t),this},n.prototype.getStep=function(e){if(null!=this._options.steps[e])return t.extend({id:"step-"+e,path:"",host:"",placement:"right",title:"",content:"<p></p>",next:e===this._options.steps.length-1?-1:e+1,prev:e-1,animation:!0,container:this._options.container,autoscroll:this._options.autoscroll,backdrop:this._options.backdrop,backdropContainer:this._options.backdropContainer,backdropPadding:this._options.backdropPadding,redirect:this._options.redirect,reflexElement:this._options.steps[e].element,backdropElement:this._options.steps[e].element,orphan:this._options.orphan,duration:this._options.duration,delay:this._options.delay,template:this._options.template,onShow:this._options.onShow,onShown:this._options.onShown,onHide:this._options.onHide,onHidden:this._options.onHidden,onNext:this._options.onNext,onPrev:this._options.onPrev,onPause:this._options.onPause,onResume:this._options.onResume,onRedirectError:this._options.onRedirectError},this._options.steps[e])},n.prototype.init=function(t){return this._force=t,this.ended()?(this._debug("Tour ended, init prevented."),this):(this.setCurrentStep(),this._initMouseNavigation(),this._initKeyboardNavigation(),null!==this._current&&this.showStep(this._current),this._inited=!0,this)},n.prototype.start=function(t){var e;return null==t&&(t=!1),this._inited||this.init(t),null===this._current&&(e=this._makePromise(null!=this._options.onStart?this._options.onStart(this):void 0),this._callOnPromiseDone(e,this.showStep,0)),this},n.prototype.next=function(){var t;return t=this.hideStep(this._current,this._current+1),this._callOnPromiseDone(t,this._showNextStep)},n.prototype.prev=function(){var t;return t=this.hideStep(this._current,this._current-1),this._callOnPromiseDone(t,this._showPrevStep)},n.prototype.goTo=function(t){var e;return e=this.hideStep(this._current,t),this._callOnPromiseDone(e,this.showStep,t)},n.prototype.end=function(){var n,o;return n=function(n){return function(o){if(t(e).off("click.tour-"+n._options.name),t(e).off("keyup.tour-"+n._options.name),n._setState("end","yes"),n._inited=!1,n._force=!1,n._clearTimer(),null!=n._options.onEnd)return n._options.onEnd(n)}}(this),o=this.hideStep(this._current),this._callOnPromiseDone(o,n)},n.prototype.ended=function(){return!this._force&&!!this._getState("end")},n.prototype.restart=function(){return this._removeState("current_step"),this._removeState("end"),this._removeState("redirect_to"),this.start()},n.prototype.pause=function(){var t;return(t=this.getStep(this._current))&&t.duration?(this._paused=!0,this._duration-=(new Date).getTime()-this._start,window.clearTimeout(this._timer),this._debug("Paused/Stopped step "+(this._current+1)+" timer ("+this._duration+" remaining)."),null!=t.onPause?t.onPause(this,this._duration):void 0):this},n.prototype.resume=function(){var t;return(t=this.getStep(this._current))&&t.duration?(this._paused=!1,this._start=(new Date).getTime(),this._duration=this._duration||t.duration,this._timer=window.setTimeout(function(t){return function(){return t._isLast()?t.next():t.end()}}(this),this._duration),this._debug("Started step "+(this._current+1)+" timer with duration "+this._duration),null!=t.onResume&&this._duration!==t.duration?t.onResume(this,this._duration):void 0):this},n.prototype.hideStep=function(e,n){var o,i,r,s;if(s=this.getStep(e))return this._clearTimer(),r=this._makePromise(null!=s.onHide?s.onHide(this,e):void 0),i=function(o){return function(i){var r,a;if((r=t(s.element)).data("bs.popover")||(r=t("body")),r.popover("dispose").removeClass("tour-"+o._options.name+"-element tour-"+o._options.name+"-"+e+"-element").removeData("bs.popover"),s.reflex&&t(s.reflexElement).removeClass("tour-step-element-reflex").off(o._reflexEvent(s.reflex)+".tour-"+o._options.name),s.backdrop&&((a=null!=n&&o.getStep(n))&&a.backdrop&&a.backdropElement===s.backdropElement||o._hideOverlayElement(s)),null!=s.onHidden)return s.onHidden(o)}}(this),o=s.delay.hide||s.delay,"[object Number]"==={}.toString.call(o)&&o>0?(this._debug("Wait "+o+" milliseconds to hide the step "+(this._current+1)),window.setTimeout(function(t){return function(){return t._callOnPromiseDone(r,i)}}(this),o)):this._callOnPromiseDone(r,i),r},n.prototype.showStep=function(t){var n,o,i,r,s,a;return this.ended()?(this._debug("Tour ended, showStep prevented."),this):(a=this.getStep(t))&&(s=t<this._current,o=this._makePromise(null!=a.onShow?a.onShow(this,t):void 0),this.setCurrentStep(t),n=function(){switch({}.toString.call(a.path)){case"[object Function]":return a.path();case"[object String]":return this._options.basePath+a.path;default:return a.path}}.call(this),!a.redirect||!this._isRedirect(a.host,n,e.location)||(this._redirect(a,t,n),this._isJustPathHashDifferent(a.host,n,e.location)))?(r=function(e){return function(n){if(e._isOrphan(a)){if(!1===a.orphan)return e._debug("Skip the orphan step "+(e._current+1)+".\nOrphan option is false and the element does not exist or is hidden."),void(s?e._showPrevStep():e._showNextStep());e._debug("Show the orphan step "+(e._current+1)+". Orphans option is true.")}if(a.autoscroll?e._scrollIntoView(t):e._showPopoverAndOverlay(t),a.duration)return e.resume()}}(this),i=a.delay.show||a.delay,"[object Number]"==={}.toString.call(i)&&i>0?(this._debug("Wait "+i+" milliseconds to show the step "+(this._current+1)),window.setTimeout(function(t){return function(){return t._callOnPromiseDone(o,r)}}(this),i)):this._callOnPromiseDone(o,r),o):void 0},n.prototype.getCurrentStep=function(){return this._current},n.prototype.setCurrentStep=function(t){return null!=t?(this._current=t,this._setState("current_step",t)):(this._current=this._getState("current_step"),this._current=null===this._current?null:parseInt(this._current,10)),this},n.prototype.redraw=function(){return this._showOverlayElement(this.getStep(this.getCurrentStep()))},n.prototype._setState=function(t,e){var n;if(this._options.storage){n=this._options.name+"_"+t;try{this._options.storage.setItem(n,e)}catch(t){t.code===DOMException.QUOTA_EXCEEDED_ERR&&this._debug("LocalStorage quota exceeded. State storage failed.")}return this._options.afterSetState(n,e)}return null==this._state&&(this._state={}),this._state[t]=e},n.prototype._removeState=function(t){var e;return this._options.storage?(e=this._options.name+"_"+t,this._options.storage.removeItem(e),this._options.afterRemoveState(e)):null!=this._state?delete this._state[t]:void 0},n.prototype._getState=function(t){var e,n;return this._options.storage?(e=this._options.name+"_"+t,n=this._options.storage.getItem(e)):null!=this._state&&(n=this._state[t]),void 0!==n&&"null"!==n||(n=null),this._options.afterGetState(t,n),n},n.prototype._showNextStep=function(){var t,e,n;return n=this.getStep(this._current),e=function(t){return function(e){return t.showStep(n.next)}}(this),t=this._makePromise(null!=n.onNext?n.onNext(this):void 0),this._callOnPromiseDone(t,e)},n.prototype._showPrevStep=function(){var t,e,n;return n=this.getStep(this._current),e=function(t){return function(e){return t.showStep(n.prev)}}(this),t=this._makePromise(null!=n.onPrev?n.onPrev(this):void 0),this._callOnPromiseDone(t,e)},n.prototype._debug=function(t){if(this._options.debug)return window.console.log("Bootstrap Tour '"+this._options.name+"' | "+t)},n.prototype._isRedirect=function(t,e,n){var o;return!(null==t||""===t||!("[object RegExp]"==={}.toString.call(t)&&!t.test(n.origin)||"[object String]"==={}.toString.call(t)&&this._isHostDifferent(t,n)))||(o=[n.pathname,n.search,n.hash].join(""),null!=e&&""!==e&&("[object RegExp]"==={}.toString.call(e)&&!e.test(o)||"[object String]"==={}.toString.call(e)&&this._isPathDifferent(e,o)))},n.prototype._isHostDifferent=function(t,e){switch({}.toString.call(t)){case"[object RegExp]":return!t.test(e.origin);case"[object String]":return this._getProtocol(t)!==this._getProtocol(e.href)||this._getHost(t)!==this._getHost(e.href);default:return!0}},n.prototype._isPathDifferent=function(t,e){return this._getPath(t)!==this._getPath(e)||!this._equal(this._getQuery(t),this._getQuery(e))||!this._equal(this._getHash(t),this._getHash(e))},n.prototype._isJustPathHashDifferent=function(t,e,n){var o;return(null==t||""===t||!this._isHostDifferent(t,n))&&(o=[n.pathname,n.search,n.hash].join(""),"[object String]"==={}.toString.call(e)&&(this._getPath(e)===this._getPath(o)&&this._equal(this._getQuery(e),this._getQuery(o))&&!this._equal(this._getHash(e),this._getHash(o))))},n.prototype._redirect=function(n,o,i){var r;return t.isFunction(n.redirect)?n.redirect.call(this,i):(r="[object String]"==={}.toString.call(n.host)?""+n.host+i:i,this._debug("Redirect to "+r),this._getState("redirect_to")!==""+o?(this._setState("redirect_to",""+o),e.location.href=r):(this._debug("Error redirection loop to "+i),this._removeState("redirect_to"),null!=n.onRedirectError?n.onRedirectError(this):void 0))},n.prototype._isOrphan=function(e){return null==e.element||!t(e.element).length||t(e.element).is(":hidden")&&"http://www.w3.org/2000/svg"!==t(e.element)[0].namespaceURI},n.prototype._isLast=function(){return this._current<this._options.steps.length-1},n.prototype._showPopoverAndOverlay=function(t){var e;if(this.getCurrentStep()===t&&!this.ended())return(e=this.getStep(t)).backdrop&&this._showOverlayElement(e),this._showPopover(e,t),null!=e.onShown&&e.onShown(this),this._debug("Step "+(this._current+1)+" of "+this._options.steps.length)},n.prototype._showPopover=function(e,n){var o,i,r;return t(".tour-"+this._options.name).remove(),r=t.extend({},this._options),i=this._isOrphan(e),e.template=this._template(e,n),i&&(e.element="body",e.placement="top"),(o=t(e.element)).addClass("tour-"+this._options.name+"-element tour-"+this._options.name+"-"+n+"-element"),e.options&&t.extend(r,e.options),e.reflex&&!i&&t(e.reflexElement).addClass("tour-step-element-reflex").off(this._reflexEvent(e.reflex)+".tour-"+this._options.name).on(this._reflexEvent(e.reflex)+".tour-"+this._options.name,function(t){return function(){return t._isLast()?t.next():t.end()}}(this)),o.popover({placement:e.placement,trigger:"manual",title:e.title,content:e.content,html:!0,animation:e.animation,container:e.container,template:e.template,selector:e.element}).popover("show"),t(o.data("bs.popover").getTipElement()).attr("id",e.id)},n.prototype._template=function(e,n){var o,i,r,s,a,p;return p=e.template,this._isOrphan(e)&&"[object Boolean]"!=={}.toString.call(e.orphan)&&(p=e.orphan),a=t(t.isFunction(p)?p(n,e):p),o=a.find(".popover-navigation"),r=o.find('[data-role="prev"]'),i=o.find('[data-role="next"]'),s=o.find('[data-role="pause-resume"]'),this._isOrphan(e)&&a.addClass("orphan"),a.addClass("tour-"+this._options.name+" tour-"+this._options.name+"-"+n),e.reflex&&a.addClass("tour-"+this._options.name+"-reflex"),e.prev<0&&r.addClass("disabled").prop("disabled",!0).prop("tabindex",-1),e.next<0&&i.addClass("disabled").prop("disabled",!0).prop("tabindex",-1),e.duration||s.remove(),a.clone().wrap("<div>").parent().html()},n.prototype._reflexEvent=function(t){return"[object Boolean]"==={}.toString.call(t)?"click":t},n.prototype._scrollIntoView=function(e){var n,o,i,r,s,a,p,u;if(p=this.getStep(e),!(n=t(p.element)).length)return this._showPopoverAndOverlay(e);switch(o=t(window),s=n.offset().top,r=n.outerHeight(),u=o.height(),a=0,p.placement){case"top":a=Math.max(0,s-u/2);break;case"left":case"right":a=Math.max(0,s+r/2-u/2);break;case"bottom":a=Math.max(0,s+r-u/2)}return this._debug("Scroll into view. ScrollTop: "+a+". Element offset: "+s+". Window height: "+u+"."),i=0,t("body, html").stop(!0,!0).animate({scrollTop:Math.ceil(a)},function(t){return function(){if(2==++i)return t._showPopoverAndOverlay(e),t._debug("Scroll into view.\nAnimation end element offset: "+n.offset().top+".\nWindow height: "+o.height()+".")}}(this))},n.prototype._initMouseNavigation=function(){var n;return n=this,t(e).off("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='prev']").off("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='next']").off("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='end']").off("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='pause-resume']").on("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='next']",function(t){return function(e){return e.preventDefault(),t.next()}}(this)).on("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='prev']",function(t){return function(e){if(e.preventDefault(),t._current>0)return t.prev()}}(this)).on("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='end']",function(t){return function(e){return e.preventDefault(),t.end()}}(this)).on("click.tour-"+this._options.name,".popover.tour-"+this._options.name+" *[data-role='pause-resume']",function(e){var o;return e.preventDefault(),(o=t(this)).text(n._paused?o.data("pause-text"):o.data("resume-text")),n._paused?n.resume():n.pause()})},n.prototype._initKeyboardNavigation=function(){if(this._options.keyboard)return t(e).on("keyup.tour-"+this._options.name,function(t){return function(e){if(e.which)switch(e.which){case 39:return e.preventDefault(),t._isLast()?t.next():t.end();case 37:if(e.preventDefault(),t._current>0)return t.prev()}}}(this))},n.prototype._makePromise=function(e){return e&&t.isFunction(e.then)?e:null},n.prototype._callOnPromiseDone=function(t,e,n){return t?t.then(function(t){return function(o){return e.call(t,n)}}(this)):e.call(this,n)},n.prototype._showBackground=function(n,o){var i,r,s,a,p,u,l,c,h;for(s=t(e).height(),h=t(e).width(),c=[],a=0,p=(l=["top","bottom","left","right"]).length;a<p;a++)switch(u=l[a],i=null!=(r=this.backdrops)[u]?r[u]:r[u]=t("<div>",{class:"tour-backdrop "+u}),t(n.backdropContainer).append(i),u){case"top":c.push(i.height(o.offset.top>0?o.offset.top:0).width(h).offset({top:0,left:0}));break;case"bottom":c.push(i.offset({top:o.offset.top+o.height,left:0}).height(s-(o.offset.top+o.height)).width(h));break;case"left":c.push(i.offset({top:o.offset.top,left:0}).height(o.height).width(o.offset.left>0?o.offset.left:0));break;case"right":c.push(i.offset({top:o.offset.top,left:o.offset.left+o.width}).height(o.height).width(h-(o.offset.left+o.width)));break;default:c.push(void 0)}return c},n.prototype._showOverlayElement=function(e){var n,o;return 0===(n=t(e.backdropElement)).length?o={width:0,height:0,offset:{top:0,left:0}}:(o={width:n.innerWidth(),height:n.innerHeight(),offset:n.offset()},n.addClass("tour-step-backdrop"),e.backdropPadding&&(o=this._applyBackdropPadding(e.backdropPadding,o))),this._showBackground(e,o)},n.prototype._hideOverlayElement=function(e){var n,o,i;t(e.backdropElement).removeClass("tour-step-backdrop"),i=this.backdrops;for(o in i)(n=i[o])&&void 0!==n.remove&&n.remove();return this.backdrops=[]},n.prototype._applyBackdropPadding=function(t,e){return"object"==typeof t?(null==t.top&&(t.top=0),null==t.right&&(t.right=0),null==t.bottom&&(t.bottom=0),null==t.left&&(t.left=0),e.offset.top=e.offset.top-t.top,e.offset.left=e.offset.left-t.left,e.width=e.width+t.left+t.right,e.height=e.height+t.top+t.bottom):(e.offset.top=e.offset.top-t,e.offset.left=e.offset.left-t,e.width=e.width+2*t,e.height=e.height+2*t),e},n.prototype._clearTimer=function(){return window.clearTimeout(this._timer),this._timer=null,this._duration=null},n.prototype._getProtocol=function(t){return(t=t.split("://")).length>1?t[0]:"http"},n.prototype._getHost=function(t){return t=t.split("//"),(t=t.length>1?t[1]:t[0]).split("/")[0]},n.prototype._getPath=function(t){return t.replace(/\/?$/,"").split("?")[0].split("#")[0]},n.prototype._getQuery=function(t){return this._getParams(t,"?")},n.prototype._getHash=function(t){return this._getParams(t,"#")},n.prototype._getParams=function(t,e){var n,o,i,r,s;if(1===(r=t.split(e)).length)return{};for(s={},n=0,o=(r=r[1].split("&")).length;n<o;n++)s[(i=(i=r[n]).split("="))[0]]=i[1]||"";return s},n.prototype._equal=function(t,e){var n,o,i,r,s,a;if("[object Object]"==={}.toString.call(t)&&"[object Object]"==={}.toString.call(e)){if(r=Object.keys(t),s=Object.keys(e),r.length!==s.length)return!1;for(o in t)if(a=t[o],!this._equal(e[o],a))return!1;return!0}if("[object Array]"==={}.toString.call(t)&&"[object Array]"==={}.toString.call(e)){if(t.length!==e.length)return!1;for(o=n=0,i=t.length;n<i;o=++n)if(a=t[o],!this._equal(a,e[o]))return!1;return!0}return t===e},n}()});